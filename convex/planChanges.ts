import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalAction, internalMutation, internalQuery, mutation, query } from "./_generated/server";
import { getStripePrices } from "./stripe/config";

// Helper function to get environment-appropriate price IDs
function getPlanPriceIds() {
  const prices = getStripePrices();
  return {
    basic: {
      monthly: prices.basic.monthly,
      annual: prices.basic.yearly,
    },
    professional: {
      monthly: prices.professional.monthly,
      annual: prices.professional.yearly,
    },
    enterprise: {
      monthly: prices.enterprise.monthly,
      annual: prices.enterprise.yearly,
    },
  };
}

// Plan configuration
const PLAN_CONFIG = {
  basic: {
    name: 'Basic',
    monthlyPrice: 299,
    annualPrice: 2870, // 299 * 12 * 0.8 = 2870 (20% discount)
    stripePriceIds: getPlanPriceIds().basic,
    features: ['5_projects', 'basic_support', 'mobile_access']
  },
  professional: {
    name: 'Professional',
    monthlyPrice: 999,
    annualPrice: 9590, // 999 * 12 * 0.8 = 9590 (20% discount)
    stripePriceIds: getPlanPriceIds().professional,
    features: ['unlimited_projects', 'team_collaboration', 'priority_support', 'api_access']
  },
  enterprise: {
    name: 'Enterprise',
    monthlyPrice: 2999,
    annualPrice: 28790, // 2999 * 12 * 0.8 = 28790 (20% discount)
    stripePriceIds: getPlanPriceIds().enterprise,
    features: ['all_professional', 'dedicated_support', 'custom_integrations', 'sla_guarantee']
  }
};

const PLAN_HIERARCHY = ['basic', 'professional', 'enterprise'];

/**
 * Calculate proration details for plan change
 */
export const calculatePlanChangeProration = query({
  args: {
    userId: v.string(),
    newPlanId: v.string(),
    newBillingInterval: v.union(v.literal("month"), v.literal("year")),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("Abonnement ikke funnet");
    }

    const currentPlan = PLAN_CONFIG[subscription.planLevel as keyof typeof PLAN_CONFIG];
    const newPlan = PLAN_CONFIG[args.newPlanId as keyof typeof PLAN_CONFIG];

    if (!currentPlan || !newPlan) {
      throw new Error("Ugyldig plan");
    }

    const currentPrice = subscription.billingInterval === 'year' 
      ? currentPlan.annualPrice 
      : currentPlan.monthlyPrice;
    
    const newPrice = args.newBillingInterval === 'year' 
      ? newPlan.annualPrice 
      : newPlan.monthlyPrice;

    // Calculate days remaining in current billing period
    const now = Date.now();
    const nextBillingDate = subscription.currentPeriodEnd || now;
    const daysInPeriod = subscription.billingInterval === 'year' ? 365 : 30;
    const daysRemaining = Math.max(0, Math.ceil((nextBillingDate - now) / (1000 * 60 * 60 * 24)));
    
    // Calculate proration
    const unusedAmount = (currentPrice * daysRemaining) / daysInPeriod;
    const newPeriodAmount = (newPrice * daysRemaining) / daysInPeriod;
    const prorationAmount = Math.round((newPeriodAmount - unusedAmount) * 100) / 100;

    const isUpgrade = PLAN_HIERARCHY.indexOf(args.newPlanId) > PLAN_HIERARCHY.indexOf(subscription.planLevel);

    return {
      currentPlan: {
        id: subscription.planLevel,
        name: currentPlan.name,
        price: currentPrice,
        billingInterval: subscription.billingInterval,
      },
      newPlan: {
        id: args.newPlanId,
        name: newPlan.name,
        price: newPrice,
        billingInterval: args.newBillingInterval,
      },
      proration: {
        amount: prorationAmount,
        description: prorationAmount > 0 
          ? `Du vil bli belastet ${Math.abs(prorationAmount)} NOK for oppgraderingen, forholdsmessig for de resterende ${daysRemaining} dagene i faktureringsperioden.`
          : prorationAmount < 0
          ? `Du vil få en kreditt på ${Math.abs(prorationAmount)} NOK som vil bli anvendt på neste faktura.`
          : 'Ingen tilleggsgebyr for denne endringen.',
      },
      effectiveDate: new Date().toISOString(),
      nextBillingDate: new Date(nextBillingDate).toISOString(),
      isUpgrade,
      daysRemaining,
    };
  },
});

/**
 * Initiate plan change process
 */
export const initiatePlanChange = mutation({
  args: {
    userId: v.string(),
    newPlanId: v.string(),
    newBillingInterval: v.union(v.literal("month"), v.literal("year")),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("Abonnement ikke funnet");
    }

    if (!subscription.stripeSubscriptionId) {
      throw new Error("Stripe abonnement-ID mangler");
    }

    // Validate plan change
    const newPlan = PLAN_CONFIG[args.newPlanId as keyof typeof PLAN_CONFIG];
    if (!newPlan) {
      throw new Error("Ugyldig plan");
    }

    // Create plan change request record
    const planChangeId = await ctx.db.insert("planChangeRequests", {
      userId: args.userId,
      subscriptionId: subscription._id,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      currentPlanId: subscription.planLevel,
      currentBillingInterval: subscription.billingInterval,
      newPlanId: args.newPlanId,
      newBillingInterval: args.newBillingInterval,
      status: "pending",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Schedule the actual plan change processing
    await ctx.scheduler.runAfter(0, internal.planChanges.processPlanChange, {
      planChangeRequestId: planChangeId,
    });

    return {
      planChangeRequestId: planChangeId,
      status: "initiated",
      message: "Planendring er startet og vil bli behandlet umiddelbart",
    };
  },
});

/**
 * Process plan change with Stripe
 */
export const processPlanChange = internalAction({
  args: {
    planChangeRequestId: v.id("planChangeRequests"),
  },
  handler: async (ctx, args) => {
    const planChangeRequest = await ctx.runQuery(internal.planChanges.getPlanChangeRequest, {
      planChangeRequestId: args.planChangeRequestId,
    });

    if (!planChangeRequest) {
      throw new Error("Plan change request not found");
    }

    if (planChangeRequest.status !== "pending") {
      throw new Error("Plan change request is not pending");
    }

    try {
      // Update status to processing
      await ctx.runMutation(internal.planChanges.updatePlanChangeStatus, {
        planChangeRequestId: args.planChangeRequestId,
        status: "processing",
      });

      // Get new plan configuration
      const newPlan = PLAN_CONFIG[planChangeRequest.newPlanId as keyof typeof PLAN_CONFIG];
      const newStripePriceId = newPlan.stripePriceIds[planChangeRequest.newBillingInterval];

      if (!newStripePriceId) {
        throw new Error(`Stripe price ID not found for ${planChangeRequest.newPlanId} ${planChangeRequest.newBillingInterval}`);
      }

      // Update Stripe subscription
      const stripeResult = await ctx.runAction(internal.planChanges.updateStripeSubscription, {
        stripeSubscriptionId: planChangeRequest.stripeSubscriptionId,
        newPriceId: newStripePriceId,
        prorationBehavior: "always_invoice", // Always create prorations
      });

      if (!stripeResult.success) {
        throw new Error(`Stripe update failed: ${stripeResult.error}`);
      }

      // Update local subscription record
      await ctx.runMutation(internal.planChanges.updateSubscriptionPlan, {
        subscriptionId: planChangeRequest.subscriptionId,
        newPlanId: planChangeRequest.newPlanId,
        newBillingInterval: planChangeRequest.newBillingInterval,
        stripeData: stripeResult.subscription,
      });

      // Update plan change request status
      const updateArgs: any = {
        planChangeRequestId: args.planChangeRequestId,
        status: "completed",
        completedAt: Date.now(),
      };

      // Only include stripeInvoiceId if it's not null
      if (stripeResult.invoiceId) {
        updateArgs.stripeInvoiceId = stripeResult.invoiceId;
      }

      await ctx.runMutation(internal.planChanges.updatePlanChangeStatus, updateArgs);

      // Send confirmation email
      await ctx.runAction(internal.planChanges.sendPlanChangeConfirmationEmail, {
        userId: planChangeRequest.userId,
        planChangeRequestId: args.planChangeRequestId,
      });

      console.log(`✅ Plan change completed for user ${planChangeRequest.userId}`);

      return {
        success: true,
        planChangeRequestId: args.planChangeRequestId,
        newPlanId: planChangeRequest.newPlanId,
        newBillingInterval: planChangeRequest.newBillingInterval,
      };

    } catch (error) {
      console.error("❌ Plan change failed:", error);

      // Update status to failed
      await ctx.runMutation(internal.planChanges.updatePlanChangeStatus, {
        planChangeRequestId: args.planChangeRequestId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        completedAt: Date.now(),
      });

      throw error;
    }
  },
});

/**
 * Get plan change request
 */
export const getPlanChangeRequest = internalQuery({
  args: {
    planChangeRequestId: v.id("planChangeRequests"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.planChangeRequestId);
  },
});

/**
 * Update plan change request status
 */
export const updatePlanChangeStatus = internalMutation({
  args: {
    planChangeRequestId: v.id("planChangeRequests"),
    status: v.string(),
    completedAt: v.optional(v.number()),
    errorMessage: v.optional(v.string()),
    stripeInvoiceId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.completedAt) updates.completedAt = args.completedAt;
    if (args.errorMessage) updates.errorMessage = args.errorMessage;
    // Only set stripeInvoiceId if it's provided and not null/undefined
    if (args.stripeInvoiceId) updates.stripeInvoiceId = args.stripeInvoiceId;

    await ctx.db.patch(args.planChangeRequestId, updates);
  },
});

/**
 * Update subscription plan in database
 */
export const updateSubscriptionPlan = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    newPlanId: v.string(),
    newBillingInterval: v.union(v.literal("month"), v.literal("year")),
    stripeData: v.any(),
  },
  handler: async (ctx, args) => {
    const newPlan = PLAN_CONFIG[args.newPlanId as keyof typeof PLAN_CONFIG];

    await ctx.db.patch(args.subscriptionId, {
      planLevel: args.newPlanId,
      billingInterval: args.newBillingInterval,
      currentPeriodStart: args.stripeData.current_period_start * 1000,
      currentPeriodEnd: args.stripeData.current_period_end * 1000,
      updatedAt: Date.now(),
    });

    // Log the plan change
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId: args.subscriptionId,
      eventType: 'plan_changed',
      eventData: {
        newPlanId: args.newPlanId,
        newBillingInterval: args.newBillingInterval,
        newAmount: newPrice,
        stripeSubscriptionId: args.stripeData.id,
      },
      timestamp: Date.now(),
      source: 'user_action',
    });
  },
});

/**
 * Update Stripe subscription
 */
export const updateStripeSubscription = internalAction({
  args: {
    stripeSubscriptionId: v.string(),
    newPriceId: v.string(),
    prorationBehavior: v.string(),
  },
  handler: async (ctx, args) => {
    const stripe = new (await import('stripe')).default(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: "2025-08-27.basil",
    });

    try {
      // Get current subscription
      const subscription = await stripe.subscriptions.retrieve(args.stripeSubscriptionId);

      if (!subscription.items.data[0]) {
        throw new Error("No subscription items found");
      }

      // Update the subscription item with new price
      const updatedSubscription = await stripe.subscriptions.update(args.stripeSubscriptionId, {
        items: [{
          id: subscription.items.data[0].id,
          price: args.newPriceId,
        }],
        proration_behavior: args.prorationBehavior as any,
      });

      // Get the latest invoice if proration was created
      let invoiceId = null;
      if (args.prorationBehavior === "always_invoice") {
        const invoices = await stripe.invoices.list({
          subscription: args.stripeSubscriptionId,
          limit: 1,
        });

        if (invoices.data[0]) {
          invoiceId = invoices.data[0].id;
        }
      }

      return {
        success: true,
        subscription: updatedSubscription,
        invoiceId,
      };

    } catch (error) {
      console.error("Stripe subscription update failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown Stripe error",
      };
    }
  },
});

/**
 * Send plan change confirmation email
 */
export const sendPlanChangeConfirmationEmail = internalAction({
  args: {
    userId: v.string(),
    planChangeRequestId: v.id("planChangeRequests"),
  },
  handler: async (ctx, args) => {
    const planChangeRequest = await ctx.runQuery(internal.planChanges.getPlanChangeRequest, {
      planChangeRequestId: args.planChangeRequestId,
    });

    if (!planChangeRequest) {
      throw new Error("Plan change request not found");
    }

    // Get user details for email
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    const currentPlan = PLAN_CONFIG[planChangeRequest.currentPlanId as keyof typeof PLAN_CONFIG];
    const newPlan = PLAN_CONFIG[planChangeRequest.newPlanId as keyof typeof PLAN_CONFIG];

    const isUpgrade = PLAN_HIERARCHY.indexOf(planChangeRequest.newPlanId) > PLAN_HIERARCHY.indexOf(planChangeRequest.currentPlanId);
    const changeType = isUpgrade ? 'oppgradering' : 'nedgradering';

    const emailContent = {
      subject: `Bekreftelse på ${changeType} av abonnement - JobbLogg`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563EB;">Abonnement ${isUpgrade ? 'oppgradert' : 'nedgradert'}</h2>

          <p>Hei ${user.name || user.email},</p>

          <p>Vi bekrefter at ditt JobbLogg-abonnement har blitt ${isUpgrade ? 'oppgradert' : 'nedgradert'}:</p>

          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Endring av abonnement</h3>
            <p><strong>Fra:</strong> ${currentPlan.name} (${planChangeRequest.currentBillingInterval === 'year' ? 'årlig' : 'månedlig'})</p>
            <p><strong>Til:</strong> ${newPlan.name} (${planChangeRequest.newBillingInterval === 'year' ? 'årlig' : 'månedlig'})</p>
            <p><strong>Endringen trådte i kraft:</strong> ${new Date().toLocaleDateString('nb-NO')}</p>
          </div>

          <h3>Hva skjer nå?</h3>
          <ul>
            <li>Du har umiddelbart tilgang til alle funksjoner i din nye plan</li>
            <li>Neste faktura vil reflektere den nye prisen</li>
            <li>Du kan endre eller avbryte abonnementet når som helst</li>
          </ul>

          <p>Hvis du har spørsmål om endringen, ikke nøl med å kontakte oss.</p>

          <p>Takk for at du bruker JobbLogg!</p>

          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">
            JobbLogg - Prosjektdokumentasjon for håndverkere<br>
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>
        </div>
      `,
    };

    await ctx.runAction(internal.emails.sendEmail, {
      to: user.email,
      subject: emailContent.subject,
      html: emailContent.html,
      recipientName: user.name || user.email,
    });

    console.log(`✅ Plan change confirmation email sent to ${user.email}`);
  },
});

/**
 * Get user's plan change history
 */
export const getPlanChangeHistory = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const planChangeRequests = await ctx.db
      .query("planChangeRequests")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(10);

    return planChangeRequests.map(request => ({
      ...request,
      currentPlanName: PLAN_CONFIG[request.currentPlanId as keyof typeof PLAN_CONFIG]?.name,
      newPlanName: PLAN_CONFIG[request.newPlanId as keyof typeof PLAN_CONFIG]?.name,
    }));
  },
});

/**
 * Check if user can change to specific plan
 */
export const canChangeToPlan = query({
  args: {
    userId: v.string(),
    targetPlanId: v.string(),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      return { canChange: false, reason: "Ingen aktiv abonnement funnet" };
    }

    if (subscription.status !== "active" && subscription.status !== "trialing") {
      return { canChange: false, reason: "Abonnementet er ikke aktivt" };
    }

    if (subscription.planLevel === args.targetPlanId) {
      return { canChange: false, reason: "Du har allerede denne planen" };
    }

    // Check for pending plan changes
    const pendingChange = await ctx.db
      .query("planChangeRequests")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.or(q.eq(q.field("status"), "pending"), q.eq(q.field("status"), "processing")))
      .first();

    if (pendingChange) {
      return { canChange: false, reason: "Du har allerede en pågående planendring" };
    }

    return { canChange: true };
  },
});
