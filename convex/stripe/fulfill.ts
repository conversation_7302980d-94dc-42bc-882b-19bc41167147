"use node";

import { v } from "convex/values";
import <PERSON><PERSON> from "stripe";
import { internal } from "../_generated/api";
import { internalAction } from "../_generated/server";

/**
 * Node action for Stripe webhook signature verification and processing
 * This MUST run in Node runtime to use the Stripe SDK
 */
export const fulfill = internalAction({
  args: {
    signature: v.string(),
    payload: v.string(),
  },
  handler: async (ctx, args) => {
    console.log("🔄 Starting Stripe webhook fulfillment in Node runtime");

    try {
      // Step 1: Check environment variables
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

      console.log("🔑 Environment check:");
      console.log("  - STRIPE_SECRET_KEY:", !!stripeSecretKey);
      console.log("  - STRIPE_WEBHOOK_SECRET:", !!webhookSecret);
      console.log("  - Webhook secret prefix:", webhookSecret?.substring(0, 15));

      if (!stripeSecretKey || stripeSecretKey === 'sk_test_your_secret_key_here') {
        console.log("❌ Stripe secret key not configured");
        return { success: false, error: "Stripe secret key not configured" };
      }

      if (!webhookSecret || webhookSecret === 'whsec_your_webhook_secret_here') {
        console.log("❌ Webhook secret not configured");
        return { success: false, error: "Webhook secret not configured" };
      }

      // Step 2: Initialize Stripe
      console.log("🔄 Initializing Stripe SDK...");
      // **FIX E**: Use clean API version without UI suffixes
      const stripe = new Stripe(stripeSecretKey);

      // **ENHANCED LOGGING E**: Log that we're using default API version
      console.log("🔧 Stripe SDK initialized with default API version (no .basil suffix)");

      // Step 3: Verify webhook signature with raw payload
      console.log("🔄 Verifying webhook signature...");
      console.log("  - Raw payload length:", args.payload.length);
      console.log("  - Signature header preview:", args.signature.substring(0, 50));
      console.log("  - Webhook secret suffix:", webhookSecret.slice(-6));

      let event: Stripe.Event;
      try {
        // Use raw payload directly with Stripe SDK
        event = stripe.webhooks.constructEvent(args.payload, args.signature, webhookSecret);
        console.log("✅ Signature verification successful!");

        // **ENHANCED LOGGING A**: Comprehensive logging immediately after signature verification
        console.log("📋 WEBHOOK_EVENT_VERIFIED:", {
          eventType: event.type,
          eventId: event.id,
          created: event.created,
          livemode: event.livemode,
          apiVersion: event.api_version,
          pendingWebhooks: event.pending_webhooks
        });

        // Log specific subscription data if this is a subscription event
        if (event.type.startsWith('customer.subscription.')) {
          const subscription = event.data.object as Stripe.Subscription;
          console.log("📋 SUBSCRIPTION_EVENT_DATA:", {
            subscriptionId: subscription.id,
            customerId: typeof subscription.customer === 'string' ? subscription.customer : subscription.customer?.id,
            status: subscription.status,
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            cancelAt: subscription.cancel_at,
            canceledAt: subscription.canceled_at,
            currentPeriodStart: (subscription as any).current_period_start,
            currentPeriodEnd: (subscription as any).current_period_end
          });

          // Log previous attributes if available (properly typed for subscription events)
          if (event.data.previous_attributes && event.type.startsWith('customer.subscription.')) {
            const prevAttrs = event.data.previous_attributes as Partial<Stripe.Subscription>;
            console.log("📋 SUBSCRIPTION_PREVIOUS_ATTRIBUTES:", {
              previousCancelAtPeriodEnd: prevAttrs.cancel_at_period_end,
              previousStatus: prevAttrs.status,
              previousCancelAt: prevAttrs.cancel_at,
              previousCanceledAt: prevAttrs.canceled_at
            });

            // **ENHANCED LOGGING A**: Explicit cancellation change detection
            if (prevAttrs.cancel_at_period_end !== undefined) {
              const prevCancel = prevAttrs.cancel_at_period_end;
              const currentCancel = subscription.cancel_at_period_end;
              if (prevCancel !== currentCancel) {
                console.log(`🔄 CANCELLATION_CHANGE_DETECTED: cancel_at_period_end changed: ${prevCancel} -> ${currentCancel} for subscription ${subscription.id}`);
              }
            }
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.log("❌ Signature verification failed:", errorMessage);
        console.log("❌ Debug info:");
        console.log("  - Error type:", err?.constructor?.name);
        console.log("  - Payload preview:", args.payload.substring(0, 100));

        // Log but don't fail - return success to avoid retries
        console.log("⚠️ Returning success to avoid retries");
        return { success: true, eventId: "unknown", eventType: "verification_failed" };
      }

      // Step 4: Process the event
      // TODO: Add idempotency checking with internal.webhooks.checkEventProcessed once API is generated
      console.log("🔄 Processing webhook event:", {
        id: event.id,
        type: event.type,
        created: event.created,
        livemode: event.livemode
      });

      // Process the event (always return success after signature verification)
      try {
        switch (event.type) {
          case 'checkout.session.completed':
            console.log("🎯 Handling checkout.session.completed");
            await handleCheckoutSessionCompleted(ctx, stripe, event);
            console.log("✅ checkout.session.completed handler completed");
            break;
          case 'customer.subscription.created':
            console.log("🎯 Handling customer.subscription.created");
            await handleSubscriptionCreated(ctx, stripe, event);
            console.log("✅ customer.subscription.created handler completed");
            break;
          case 'customer.subscription.updated':
            console.log("🎯 Handling customer.subscription.updated");
            await handleSubscriptionUpdated(ctx, stripe, event);
            console.log("✅ customer.subscription.updated handler completed");
            break;
          case 'invoice.payment_failed':
            console.log("🎯 Handling invoice.payment_failed");
            await handleInvoicePaymentFailed(ctx, stripe, event);
            console.log("✅ invoice.payment_failed handler completed");
            break;
          case 'invoice.payment_succeeded':
            console.log("🎯 Handling invoice.payment_succeeded");
            await handleInvoicePaymentSucceeded(ctx, stripe, event);
            console.log("✅ invoice.payment_succeeded handler completed");
            break;
          case 'checkout.session.async_payment_succeeded':
            console.log("🎯 Handling checkout.session.async_payment_succeeded");
            await handleCheckoutSessionCompleted(ctx, stripe, event);
            console.log("✅ checkout.session.async_payment_succeeded handler completed");
            break;
          case 'invoice.paid':
            console.log("ℹ️ Invoice paid - no action needed");
            break;
          default:
            console.log(`ℹ️ Unhandled event type: ${event.type} - logging and continuing`);
        }

        console.log("✅ Webhook event processed successfully");

      } catch (processingError) {
        const errorMessage = processingError instanceof Error ? processingError.message : 'Unknown processing error';
        console.log("❌ Event processing failed:", errorMessage);
        // Log error but still return success to avoid retries
      }

      // Always return success after signature verification to avoid retries
      return { success: true, eventId: event.id, eventType: event.type };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log("❌ Fulfillment action error:", errorMessage);
      return { success: false, error: errorMessage };
    }
  },
});

// ===== EVENT HANDLERS =====

/**
 * Handle checkout.session.completed events
 * This is triggered when a customer completes checkout
 */
async function handleCheckoutSessionCompleted(ctx: any, stripe: Stripe, event: Stripe.Event) {
  console.log("🛒 Processing checkout.session.completed");
  console.log("🚀 VERSION: 2025-01-10-FIXED");

  try {
    const session = event.data.object as Stripe.Checkout.Session;
    console.log("📋 Session details:", {
      id: session.id,
      mode: session.mode,
      status: session.status,
      customer: typeof session.customer,
      subscription: typeof session.subscription,
      metadata: session.metadata
    });

    // Only process subscription checkouts
    if (session.mode !== 'subscription' || !session.subscription) {
      console.log("ℹ️ Skipping non-subscription checkout");
      return;
    }

    // Handle subscription ID (can be string or object)
    const subscriptionId = typeof session.subscription === 'string'
      ? session.subscription
      : session.subscription?.id;

    if (!subscriptionId) {
      console.log("❌ No subscription ID found");
      return;
    }

    // Get the full subscription from Stripe with expanded data
    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['customer', 'items.data.price']
    });

    console.log("📋 Subscription details:", {
      id: subscription.id,
      status: subscription.status,
      customer: typeof subscription.customer
    });

    // For "pay before trial ends": ensure billing anchors to NOW for correct next renewal
    let finalSub = subscription;
    let previousStatus = subscription.status;

    const trialEnd = (subscription as any).trial_end;
    const currentPeriodEnd = (subscription as any).current_period_end;
    const stillAnchoredToTrial = !!trialEnd && currentPeriodEnd === trialEnd;

    // Check DB to catch cases where Stripe cleared trial_end but kept anchor
    let anchoredConsideringDb = stillAnchoredToTrial;
    try {
      const existing = await ctx.runQuery(
        "subscriptions:getByStripeSubscriptionIdInternal",
        { stripeSubscriptionId: subscription.id }
      );
      const existingTrialEndSec = existing?.trialEnd ? Math.floor(existing.trialEnd / 1000) : undefined;
      if (!anchoredConsideringDb && existingTrialEndSec) {
        anchoredConsideringDb = currentPeriodEnd === existingTrialEndSec;
      }
      console.log("🔍 Anchor check (checkout):", { stillAnchoredToTrial, anchoredConsideringDb, currentPeriodEnd, trialEnd, existingTrialEndSec });
    } catch (error) {
      console.log("⚠️ DB anchor check failed (checkout):", error);
    }

    if (session.payment_status === 'paid' && (subscription.status === 'trialing' || anchoredConsideringDb)) {
      console.log("🔄 Payment completed - adjusting billing anchor to NOW");
      console.log("📋 Previous status:", previousStatus, " stillAnchoredToTrial:", stillAnchoredToTrial);

      const updateParams: any = {
        billing_cycle_anchor: 'now',      // Next renewal becomes one period from NOW
        proration_behavior: 'none',
        cancel_at_period_end: false,
      };
      if (subscription.status === 'trialing' || (trialEnd && trialEnd * 1000 > Date.now())) {
        updateParams.trial_end = 'now';
      }

      await stripe.subscriptions.update(subscription.id, updateParams);

      // CRITICAL: Re-fetch the updated subscription to get correct current_period_start/end
      finalSub = await stripe.subscriptions.retrieve(subscription.id, {
        expand: ['customer', 'items.data.price', 'latest_invoice']
      });

      console.log("✅ Billing anchor updated, new status:", finalSub.status);
      console.log("📅 Updated periods:", {
        start: new Date((finalSub as any).current_period_start * 1000).toISOString(),
        end: new Date((finalSub as any).current_period_end * 1000).toISOString()
      });
    } else {
      // Ensure we use a fresh subscription snapshot regardless
      finalSub = await stripe.subscriptions.retrieve(subscription.id, {
        expand: ['customer', 'items.data.price', 'latest_invoice']
      });
    }

    // Handle customer ID (can be string or object)
    const customerId = typeof finalSub.customer === 'string'
      ? finalSub.customer
      : finalSub.customer?.id;

    if (!customerId) {
      console.log("❌ No customer ID found");
      return;
    }

    // Resolve userId from Stripe customer mapping (authoritative), abort if not found
    const mapping = await ctx.runQuery("subscriptions:getByStripeCustomerIdInternal", { stripeCustomerId: customerId });
    const resolvedUserId = mapping?.userId;
    if (!resolvedUserId) {
      console.log(`❌ MAPPING_FAILED: stripeCustomerId=${customerId} - no userId found, aborting upsert`);
      return;
    }

    console.log("🔄 Upserting subscription in database with final status:", finalSub.status);
    console.log("📋 Customer ID:", customerId);
    console.log("📋 Subscription ID:", finalSub.id);

    // Log period dates for verification
    if ((finalSub as any).current_period_start) {
      console.log("📅 Current period start:", new Date((finalSub as any).current_period_start * 1000).toISOString());
    }
    if ((finalSub as any).current_period_end) {
      console.log("📅 Current period end:", new Date((finalSub as any).current_period_end * 1000).toISOString());
    }

    // Resolve period fields with fallback if Stripe hasn't populated them yet
    let periodStartMs = (finalSub as any).current_period_start ? (finalSub as any).current_period_start * 1000 : undefined;
    let periodEndMs = (finalSub as any).current_period_end ? (finalSub as any).current_period_end * 1000 : undefined;
    if (periodStartMs === undefined || periodEndMs === undefined) {
      try {
        // **FIX D**: Skip invoice preview if subscription is cancelled
        if (finalSub.cancel_at_period_end) {
          console.log("ℹ️ Skipping invoice preview - subscription is cancelled at period end");
        } else {
          // Nytt i Basil: bruk createPreview i stedet for retrieveUpcoming
          const preview = await stripe.invoices.createPreview({
            customer: customerId as string,
            subscription: finalSub.id as string, // pass på å spesifisere subscription i Basil
          });

          const ps =
            (preview as any)?.period_start ??
            (preview as any)?.lines?.data?.[0]?.period?.start;
          const pe =
            (preview as any)?.period_end ??
            (preview as any)?.lines?.data?.[0]?.period?.end;

          if (typeof ps === 'number' && typeof pe === 'number') {
            periodStartMs = ps * 1000;
            periodEndMs = pe * 1000;
            console.log('🧮 Fallback periods from preview invoice:', {
              start: new Date(periodStartMs).toISOString(),
              end: new Date(periodEndMs).toISOString(),
            });
          } else {
            console.log('ℹ️ Preview invoice did not contain period fields');
          }
        }
      } catch (error) {
        // **FIX D**: Handle invoice_upcoming_none error gracefully
        if (error instanceof Error && error.message.includes('invoice_upcoming_none')) {
          console.log('ℹ️ Expected after cancellation: No upcoming invoice available (invoice_upcoming_none)');
        } else {
          console.log('⚠️ Failed to preview invoice for periods:', error);
        }
      }
    }

    // Structured log before DB write and build upsert payload
    const interval = extractBillingInterval(finalSub);
    const plan = normalizePlanLevel(extractPlanLevel(finalSub));
    const startIso = periodStartMs ? new Date(periodStartMs).toISOString() : 'undefined';
    const endIso = periodEndMs ? new Date(periodEndMs).toISOString() : 'undefined';
    const transition = previousStatus === 'trialing' && finalSub.status === 'active' ? 'trial→active' : 'none';
    // **ENHANCED LOGGING B**: Include cancelAtPeriodEnd in SUBSCRIPTION_UPSERT log
    console.log(`🔄 SUBSCRIPTION_UPSERT: eventType=${event.type} stripeCustomerId=${customerId} stripeSubscriptionId=${finalSub.id} userId=${resolvedUserId} status=${finalSub.status} billingInterval=${interval} currentPeriodStart=${startIso} currentPeriodEnd=${endIso} planLevel=${plan} seats=1 transition=${transition} cancelAtPeriodEnd=${finalSub.cancel_at_period_end}`);

    // Build upsert payload from re-fetched finalSub
    const upsertPayload: any = {
      userId: resolvedUserId,
      stripeSubscriptionId: finalSub.id,
      stripeCustomerId: customerId,
      status: finalSub.status,
      planLevel: normalizePlanLevel(extractPlanLevel(finalSub)),
      billingInterval: extractBillingInterval(finalSub),
      currentPeriodStart: periodStartMs,
      currentPeriodEnd: periodEndMs,
      trialStart: (finalSub as any).trial_start ? (finalSub as any).trial_start * 1000 : undefined,
      trialEnd: (finalSub as any).trial_end ? (finalSub as any).trial_end * 1000 : undefined,
      seats: 1,
      // **FIX 1**: Include cancellation fields from finalSub
      cancelAtPeriodEnd: finalSub.cancel_at_period_end,
      cancelAt: finalSub.cancel_at ? finalSub.cancel_at * 1000 : undefined,
      canceledAt: finalSub.canceled_at ? finalSub.canceled_at * 1000 : undefined,
    };

    // Set trial converted timestamp if we went from trialing -> active
    if (previousStatus === 'trialing' && finalSub.status === 'active') {
      upsertPayload.trialConvertedAt = Date.now();
      console.log("🎉 Trial converted to active subscription");
    }

    // Upsert subscription in database (idempotent) using final subscription
    await ctx.runMutation(internal.subscriptions.upsertFromStripe.upsertFromStripe, upsertPayload);

    console.log("✅ Checkout session processed successfully");

  } catch (error) {
    console.log("❌ Error processing checkout session:", error);
    // Don't throw - return success to avoid retries
  }
}

/**
 * Handle customer.subscription.created events
 */
async function handleSubscriptionCreated(ctx: any, stripe: Stripe, event: Stripe.Event) {
  console.log("📝 Processing customer.subscription.created");

  try {
    const subscription = event.data.object as Stripe.Subscription;

    // Only try to realign if there's evidence of payment on creation
    let finalSub = subscription;
    let endTrial = { changed: false, subscription } as any;
    try {
      const latestInvoiceId = typeof (subscription as any).latest_invoice === 'string'
        ? (subscription as any).latest_invoice
        : (subscription as any).latest_invoice?.id;
      if (latestInvoiceId) {
        const inv = await stripe.invoices.retrieve(latestInvoiceId as string);
        const paid = (inv as any)?.paid === true || (inv as any)?.status === 'paid' || ((inv as any)?.amount_paid ?? 0) > 0;
        if (paid) {
          endTrial = await endTrialNowIfNeeded(ctx, stripe, subscription.id);
          finalSub = endTrial.subscription;
        }
        console.log("🧾 subscription.created latest_invoice:", { id: latestInvoiceId, paid: (inv as any)?.paid, status: (inv as any)?.status, amount_paid: (inv as any)?.amount_paid });
      }
    } catch (error) {
      console.log("⚠️ Failed checking payment on subscription.created:", error);
    }

    if (endTrial.changed) {
      console.log("✅ Trial was force-ended in subscription.created, using updated subscription");
    }

    // Handle customer ID (can be string or object)
    const customerId = typeof finalSub.customer === 'string'
      ? finalSub.customer
      : finalSub.customer?.id;

    if (!customerId) {
      console.log("❌ No customer ID found");
      return;
    }

    // Resolve userId mapping from Stripe customer; abort if not found
    const mappingC = await ctx.runQuery("subscriptions:getByStripeCustomerIdInternal", { stripeCustomerId: customerId });
    const resolvedUserIdC = mappingC?.userId;
    if (!resolvedUserIdC) {
      console.log(`❌ MAPPING_FAILED: stripeCustomerId=${customerId} - no userId found, aborting upsert`);
      return;
    }

    console.log("🔄 Upserting subscription in database with final status:", finalSub.status);
    console.log("📋 Customer ID:", customerId);
    console.log("📋 Subscription ID:", finalSub.id);

    // Resolve period fields with fallback if Stripe hasn't populated them yet
    let periodStartMsC = (finalSub as any).current_period_start ? (finalSub as any).current_period_start * 1000 : undefined;
    let periodEndMsC = (finalSub as any).current_period_end ? (finalSub as any).current_period_end * 1000 : undefined;
    if (periodStartMsC === undefined || periodEndMsC === undefined) {
      try {
  // Basil: bruk preview i stedet for upcoming
  const previewC = await stripe.invoices.createPreview({
    customer: customerId as string,
    subscription: finalSub.id as string,
  });

  const psC =
    (previewC as any)?.period_start ??
    (previewC as any)?.lines?.data?.[0]?.period?.start;
  const peC =
    (previewC as any)?.period_end ??
    (previewC as any)?.lines?.data?.[0]?.period?.end;

  if (typeof psC === 'number' && typeof peC === 'number') {
    periodStartMsC = psC * 1000;
    periodEndMsC = peC * 1000;
    console.log('🧮 Fallback periods (subscription.created via preview):', {
      start: new Date(periodStartMsC).toISOString(),
      end: new Date(periodEndMsC).toISOString(),
    });
  } else {
    console.log('ℹ️ Preview invoice manglet period-felter (subscription.created)');
  }
} catch (error) {
  console.log('⚠️ Failed to preview invoice for periods (subscription.created):', error);
}

    }

    // Structured logging before DB write
    const intervalC = extractBillingInterval(finalSub);
    const planC = normalizePlanLevel(extractPlanLevel(finalSub));
    const startIsoC = periodStartMsC ? new Date(periodStartMsC).toISOString() : 'undefined';
    const endIsoC = periodEndMsC ? new Date(periodEndMsC).toISOString() : 'undefined';
    const transitionC = endTrial.changed && finalSub.status === 'active' ? 'trial→active' : 'none';
    // **ENHANCED LOGGING B**: Include cancelAtPeriodEnd in SUBSCRIPTION_UPSERT log
    console.log(`🔄 SUBSCRIPTION_UPSERT: eventType=${event.type} stripeCustomerId=${customerId} stripeSubscriptionId=${finalSub.id} userId=${resolvedUserIdC} status=${finalSub.status} billingInterval=${intervalC} currentPeriodStart=${startIsoC} currentPeriodEnd=${endIsoC} planLevel=${planC} seats=1 transition=${transitionC} cancelAtPeriodEnd=${finalSub.cancel_at_period_end}`);

    // Build upsert payload from re-fetched finalSub
    const upsertPayload: any = {
      userId: resolvedUserIdC,
      stripeSubscriptionId: finalSub.id,
      stripeCustomerId: customerId,
      status: finalSub.status,
      planLevel: planC,
      billingInterval: intervalC,
      currentPeriodStart: periodStartMsC,
      currentPeriodEnd: periodEndMsC,
      trialStart: (finalSub as any).trial_start ? (finalSub as any).trial_start * 1000 : undefined,
      trialEnd: (finalSub as any).trial_end ? (finalSub as any).trial_end * 1000 : undefined,
      seats: 1,
      // **FIX 1**: Include cancellation fields from finalSub
      cancelAtPeriodEnd: finalSub.cancel_at_period_end,
      cancelAt: finalSub.cancel_at ? finalSub.cancel_at * 1000 : undefined,
      canceledAt: finalSub.canceled_at ? finalSub.canceled_at * 1000 : undefined,
    };

    // Set trial converted timestamp if trial was force-ended
    if (endTrial.changed && finalSub.status === 'active') {
      upsertPayload.trialConvertedAt = Date.now();
      console.log("🎉 Trial converted to active subscription in subscription.created");
    }

    // Upsert subscription (idempotent - handles existing trial rows) using final subscription
    await ctx.runMutation(internal.subscriptions.upsertFromStripe.upsertFromStripe, upsertPayload);

    console.log("✅ Subscription created/updated successfully");

  } catch (error) {
    console.log("❌ Error processing subscription created:", error);
    // Don't throw - return success to avoid retries
  }
}

/**
 * Handle customer.subscription.updated events
 */
async function handleSubscriptionUpdated(ctx: any, stripe: Stripe, event: Stripe.Event) {
  console.log("🔄 Processing customer.subscription.updated");

  try {
    const subscription = event.data.object as Stripe.Subscription;

    // Prefer to realign only when status moved trialing->active or invoice is paid
    const prev = (event as any)?.data?.previous_attributes as any | undefined;
    const statusChangedToActive = prev?.status === 'trialing' && subscription.status === 'active';

    let finalSub = subscription;
    let endTrial = { changed: false, subscription } as any;

    let invoicePaid = false;
    try {
      const latestInvoiceId = typeof (subscription as any).latest_invoice === 'string'
        ? (subscription as any).latest_invoice
        : (subscription as any).latest_invoice?.id;
      if (latestInvoiceId) {
        const inv = await stripe.invoices.retrieve(latestInvoiceId as string);
        invoicePaid = (inv as any)?.paid === true || (inv as any)?.status === 'paid' || ((inv as any)?.amount_paid ?? 0) > 0;
        console.log("🧾 subscription.updated latest_invoice:", { id: latestInvoiceId, paid: (inv as any)?.paid, status: (inv as any)?.status, amount_paid: (inv as any)?.amount_paid });
      }
    } catch (error) {
      console.log("⚠️ Failed checking latest invoice on subscription.updated:", error);
    }

    if (statusChangedToActive || invoicePaid) {
      endTrial = await endTrialNowIfNeeded(ctx, stripe, subscription.id);
      finalSub = endTrial.subscription;
    }

    if (endTrial.changed) {
      console.log("✅ Trial was force-ended in subscription.updated (fallback), using updated subscription");
    }

    // Handle customer ID (can be string or object)
    const customerId = typeof finalSub.customer === 'string'
      ? finalSub.customer
      : finalSub.customer?.id;

    if (!customerId) {
      console.log("❌ No customer ID found");
      return;
    }

    // Resolve userId mapping from Stripe customer; abort if not found
    const mappingU = await ctx.runQuery("subscriptions:getByStripeCustomerIdInternal", { stripeCustomerId: customerId });
    const resolvedUserIdU = mappingU?.userId;
    if (!resolvedUserIdU) {
      console.log(`❌ MAPPING_FAILED: stripeCustomerId=${customerId} - no userId found, aborting upsert`);
      return;
    }

    console.log("🔄 Upserting subscription in database with final status:", finalSub.status);
    console.log("📋 Customer ID:", customerId);
    console.log("📋 Subscription ID:", finalSub.id);

    // Log period dates for verification
    if ((finalSub as any).current_period_start) {
      console.log("📅 Current period start:", new Date((finalSub as any).current_period_start * 1000).toISOString());
    }
    if ((finalSub as any).current_period_end) {
      console.log("📅 Current period end:", new Date((finalSub as any).current_period_end * 1000).toISOString());
    }

    // Resolve period fields with fallback if Stripe hasn't populated them yet
    let periodStartMs2 = (finalSub as any).current_period_start ? (finalSub as any).current_period_start * 1000 : undefined;
    let periodEndMs2 = (finalSub as any).current_period_end ? (finalSub as any).current_period_end * 1000 : undefined;
    if (periodStartMs2 === undefined || periodEndMs2 === undefined) {
      try {
  const custId =
    typeof finalSub.customer === 'string'
      ? finalSub.customer
      : finalSub.customer?.id;

  if (custId) {
    try {
      // **FIX D**: Skip invoice preview if subscription is cancelled
      if (finalSub.cancel_at_period_end) {
        console.log("ℹ️ Skipping invoice preview - subscription is cancelled at period end");
      } else {
        // Basil/ny SDK: createPreview i stedet for retrieveUpcoming
        const preview2 = await stripe.invoices.createPreview({
          customer: custId as string,
          subscription: finalSub.id as string,
        });

        const ps2 =
          (preview2 as any)?.period_start ??
          (preview2 as any)?.lines?.data?.[0]?.period?.start;
        const pe2 =
          (preview2 as any)?.period_end ??
          (preview2 as any)?.lines?.data?.[0]?.period?.end;

        if (typeof ps2 === 'number' && typeof pe2 === 'number') {
          periodStartMs2 = ps2 * 1000;
          periodEndMs2 = pe2 * 1000;
          console.log('🧮 Fallback periods (subscription.updated via preview):', {
            start: new Date(periodStartMs2).toISOString(),
            end: new Date(periodEndMs2).toISOString(),
          });
        } else {
          console.log('ℹ️ Preview manglet period-felter (subscription.updated)');
        }
      }
    } catch (error) {
      // **FIX D**: Handle invoice_upcoming_none error gracefully
      if (error instanceof Error && error.message.includes('invoice_upcoming_none')) {
        console.log('ℹ️ Expected after cancellation: No upcoming invoice available (invoice_upcoming_none)');
      } else {
        console.log('⚠️ Failed to preview invoice for periods (subscription.updated):', error);
      }
    }
  }
} catch (error) {
  console.log('⚠️ Failed to preview invoice for periods (subscription.updated):', error);
}

    }

    // Structured logging before DB write
    const intervalU = extractBillingInterval(finalSub);
    const planU = normalizePlanLevel(extractPlanLevel(finalSub));
    const startIsoU = periodStartMs2 ? new Date(periodStartMs2).toISOString() : 'undefined';
    const endIsoU = periodEndMs2 ? new Date(periodEndMs2).toISOString() : 'undefined';
    const transitionU = endTrial.changed && finalSub.status === 'active' ? 'trial→active' : 'none';
    // **ENHANCED LOGGING B**: Include cancelAtPeriodEnd in SUBSCRIPTION_UPSERT log
    console.log(`🔄 SUBSCRIPTION_UPSERT: eventType=${event.type} stripeCustomerId=${customerId} stripeSubscriptionId=${finalSub.id} userId=${resolvedUserIdU} status=${finalSub.status} billingInterval=${intervalU} currentPeriodStart=${startIsoU} currentPeriodEnd=${endIsoU} planLevel=${planU} seats=1 transition=${transitionU} cancelAtPeriodEnd=${finalSub.cancel_at_period_end}`);

    // Build upsert payload from re-fetched finalSub
    const upsertPayload: any = {
      userId: resolvedUserIdU,
      stripeSubscriptionId: finalSub.id,
      stripeCustomerId: customerId,
      status: finalSub.status,
      planLevel: planU,
      billingInterval: intervalU,
      currentPeriodStart: periodStartMs2,
      currentPeriodEnd: periodEndMs2,
      trialStart: (finalSub as any).trial_start ? (finalSub as any).trial_start * 1000 : undefined,
      trialEnd: (finalSub as any).trial_end ? (finalSub as any).trial_end * 1000 : undefined,
      seats: 1,
      // **FIX 1**: Include cancellation fields from finalSub
      cancelAtPeriodEnd: finalSub.cancel_at_period_end,
      cancelAt: finalSub.cancel_at ? finalSub.cancel_at * 1000 : undefined,
      canceledAt: finalSub.canceled_at ? finalSub.canceled_at * 1000 : undefined,
    };

    // Set trial converted timestamp if trial was force-ended
    if (endTrial.changed && finalSub.status === 'active') {
      upsertPayload.trialConvertedAt = Date.now();
      console.log("🎉 Trial converted to active subscription in subscription.updated");
    }

    // Upsert subscription (idempotent) using final subscription
    await ctx.runMutation(internal.subscriptions.upsertFromStripe.upsertFromStripe, upsertPayload);

    console.log("✅ Subscription updated successfully");

  } catch (error) {
    console.log("❌ Error processing subscription updated:", error);
    // Don't throw - return success to avoid retries
  }
}

/**
 * Handle invoice.payment_failed events
 */
async function handleInvoicePaymentFailed(ctx: any, stripe: Stripe, event: Stripe.Event) {
  console.log("💳 Processing invoice.payment_failed");

  try {
    const invoice = event.data.object as Stripe.Invoice;

    // Handle subscription ID (can be string or null)
    const subscriptionId = typeof (invoice as any).subscription === 'string'
      ? (invoice as any).subscription
      : (invoice as any).subscription?.id;

    if (!subscriptionId) {
      console.log("ℹ️ Skipping non-subscription invoice");
      return;
    }

    // Get subscription details
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Handle customer ID (can be string or object)
    const customerId = typeof subscription.customer === 'string'
      ? subscription.customer
      : subscription.customer?.id;

    if (!customerId) {
      console.log("❌ No customer ID found");
      return;
    }

    console.log("🔄 Handling payment failure - upserting subscription");

    // Upsert subscription status to reflect payment failure
    await ctx.runMutation(internal.subscriptions.upsertFromStripe.upsertFromStripe, {
      stripeSubscriptionId: subscription.id,
      stripeCustomerId: customerId,
      status: subscription.status,
      planLevel: extractPlanLevel(subscription),
      billingInterval: extractBillingInterval(subscription),
      currentPeriodStart: (subscription as any).current_period_start ? (subscription as any).current_period_start * 1000 : undefined,
      currentPeriodEnd: (subscription as any).current_period_end ? (subscription as any).current_period_end * 1000 : undefined,
      trialStart: (subscription as any).trial_start ? (subscription as any).trial_start * 1000 : undefined,
      trialEnd: (subscription as any).trial_end ? (subscription as any).trial_end * 1000 : undefined,
      seats: 1,
      // **FIX 4**: Include cancellation fields to prevent overwriting
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      cancelAt: subscription.cancel_at ? subscription.cancel_at * 1000 : undefined,
      canceledAt: subscription.canceled_at ? subscription.canceled_at * 1000 : undefined,
    });

    console.log("✅ Payment failure handled successfully");

  } catch (error) {
    console.log("❌ Error processing payment failure:", error);
    // Don't throw - return success to avoid retries
  }
}

/**
 * Handle invoice.payment_succeeded events
 */
async function handleInvoicePaymentSucceeded(ctx: any, stripe: Stripe, event: Stripe.Event) {
  console.log("💳 Processing invoice.payment_succeeded");

  try {
    const invoice = event.data.object as Stripe.Invoice;

    const subscriptionId = typeof (invoice as any).subscription === 'string'
      ? (invoice as any).subscription
      : (invoice as any).subscription?.id;

    if (!subscriptionId) {
      console.log("ℹ️ Skipping non-subscription invoice");
      return;
    }

    // Ensure billing anchor is correct and end trial if needed
    const endTrial = await endTrialNowIfNeeded(ctx, stripe, subscriptionId);
    let subscription = endTrial.subscription;

    // Ensure period fields are present (they can lag right after update)
    for (let i = 0; i < 5; i++) {
      const cps = (subscription as any).current_period_start;
      const cpe = (subscription as any).current_period_end;
      const havePeriods = typeof cps === 'number' && typeof cpe === 'number' && cps > 0 && cpe > 0;
      if (havePeriods) break;
      if (i === 4) {
        console.log("⚠️ Missing current period fields after retries; skipping upsert to avoid writing stale trial anchor.");
        return;
      }
      console.log(`⏳ Waiting for period fields before upsert (attempt ${i + 1})...`);
      const fresh = await stripe.subscriptions.retrieve(subscription.id, { expand: ['latest_invoice'] });
      subscription = fresh;
      await new Promise((r) => setTimeout(r, 300));
    }

    const customerId = typeof subscription.customer === 'string'
      ? subscription.customer
      : subscription.customer?.id;

    if (!customerId) {
      console.log("❌ No customer ID found on subscription");
      return;
    }

    // Resolve userId mapping from Stripe customer; abort if not found
    const mappingI = await ctx.runQuery("subscriptions:getByStripeCustomerIdInternal", { stripeCustomerId: customerId });
    const userId = mappingI?.userId;
    if (!userId) {
      console.log(`❌ MAPPING_FAILED: stripeCustomerId=${customerId} - no userId found, aborting upsert`);
      return;
    }

    // Resolve period fields with fallback if Stripe hasn't populated them yet
    let periodStartMsI = (subscription as any).current_period_start ? (subscription as any).current_period_start * 1000 : undefined;
    let periodEndMsI = (subscription as any).current_period_end ? (subscription as any).current_period_end * 1000 : undefined;
    if (periodStartMsI === undefined || periodEndMsI === undefined) {
      try {
  const custIdI =
    typeof subscription.customer === 'string'
      ? subscription.customer
      : subscription.customer?.id;

  if (custIdI) {
    try {
      // **FIX D**: Skip invoice preview if subscription is cancelled
      if (subscription.cancel_at_period_end) {
        console.log("ℹ️ Skipping invoice preview - subscription is cancelled at period end");
      } else {
        const previewI = await stripe.invoices.createPreview({
          customer: custIdI as string,
          subscription: subscription.id as string,
        });

        const psI =
          (previewI as any)?.period_start ??
          (previewI as any)?.lines?.data?.[0]?.period?.start;
        const peI =
          (previewI as any)?.period_end ??
          (previewI as any)?.lines?.data?.[0]?.period?.end;

        if (typeof psI === 'number' && typeof peI === 'number') {
          periodStartMsI = psI * 1000;
          periodEndMsI = peI * 1000;
          console.log('🧮 Fallback periods (invoice.payment_succeeded via preview):', {
            start: new Date(periodStartMsI).toISOString(),
            end: new Date(periodEndMsI).toISOString(),
          });
        } else {
          console.log('ℹ️ Preview manglet period-felter (invoice.payment_succeeded)');
        }
      }
    } catch (error) {
      // **FIX D**: Handle invoice_upcoming_none error gracefully
      if (error instanceof Error && error.message.includes('invoice_upcoming_none')) {
        console.log('ℹ️ Expected after cancellation: No upcoming invoice available (invoice_upcoming_none)');
      } else {
        console.log('⚠️ Failed to preview invoice for periods (invoice.payment_succeeded):', error);
      }
    }
  }
} catch (error) {
  console.log('⚠️ Failed to preview invoice for periods (invoice.payment_succeeded):', error);
}

    }

    // Structured logging before DB write
    const intervalI = extractBillingInterval(subscription);
    const planI = normalizePlanLevel(extractPlanLevel(subscription));
    const startIsoI = periodStartMsI ? new Date(periodStartMsI).toISOString() : 'undefined';
    const endIsoI = periodEndMsI ? new Date(periodEndMsI).toISOString() : 'undefined';
    const transitionI = endTrial.changed && subscription.status === 'active' ? 'trial→active' : 'none';
    // **ENHANCED LOGGING B**: Include cancelAtPeriodEnd in SUBSCRIPTION_UPSERT log
    console.log(`\ud83d\udd04 SUBSCRIPTION_UPSERT: eventType=${event.type} stripeCustomerId=${customerId} stripeSubscriptionId=${subscription.id} userId=${userId} status=${subscription.status} billingInterval=${intervalI} currentPeriodStart=${startIsoI} currentPeriodEnd=${endIsoI} planLevel=${planI} seats=1 transition=${transitionI} cancelAtPeriodEnd=${subscription.cancel_at_period_end}`);

    await ctx.runMutation(internal.subscriptions.upsertFromStripe.upsertFromStripe, {
      userId,
      stripeSubscriptionId: subscription.id,
      stripeCustomerId: customerId,
      status: subscription.status,
      planLevel: extractPlanLevel(subscription),
      billingInterval: extractBillingInterval(subscription),
      currentPeriodStart: periodStartMsI,
      currentPeriodEnd: periodEndMsI,
      trialStart: (subscription as any).trial_start ? (subscription as any).trial_start * 1000 : undefined,
      trialEnd: (subscription as any).trial_end ? (subscription as any).trial_end * 1000 : undefined,
      trialConvertedAt: endTrial.changed ? Date.now() : undefined,
      seats: 1,
      // **FIX 4**: Include cancellation fields to prevent overwriting
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      cancelAt: subscription.cancel_at ? subscription.cancel_at * 1000 : undefined,
      canceledAt: subscription.canceled_at ? subscription.canceled_at * 1000 : undefined,
    });

    console.log("✅ Payment success handled successfully");
  } catch (error) {
    // Don't throw - return success to avoid retries
  }
}


// ===== HELPER FUNCTIONS =====

/**
 * Normalize plan level from various sources to internal values
 */
function normalizePlanLevel(input?: string): "basic" | "professional" | "enterprise" {
  const map: Record<string, "basic"|"professional"|"enterprise"> = {
    basic: "basic",
    professional: "professional",
    enterprise: "enterprise",
    liten_bedrift: "basic",
    mellomstor_bedrift: "professional",
    stor_bedrift: "enterprise",
  };
  return map[(input || "").toLowerCase()] ?? "basic";
}

/**
 * End trial immediately if subscription is still trialing
 * This ensures immediate activation after payment
 */
async function endTrialNowIfNeeded(ctx: any, stripe: Stripe, subscriptionId: string) {
  console.log("🔄 Checking if trial needs to be ended for subscription:", subscriptionId);

  // Fetch fresh subscription
  const sub = await stripe.subscriptions.retrieve(subscriptionId);
  console.log("📋 Current subscription status:", sub.status);

  const trialEnd = (sub as any).trial_end;
  const currentPeriodEnd = (sub as any).current_period_end;
  const stillAnchoredToTrial = !!trialEnd && currentPeriodEnd === trialEnd;

  // Check DB-stored trialEnd to catch cases where Stripe cleared trial_end but kept anchor
  let anchoredConsideringDb = stillAnchoredToTrial;
  try {
    const existing = await ctx.runQuery(
      "subscriptions:getByStripeSubscriptionIdInternal",
      { stripeSubscriptionId: subscriptionId }
    );
    const existingTrialEndSec = existing?.trialEnd ? Math.floor(existing.trialEnd / 1000) : undefined;
    if (!anchoredConsideringDb && existingTrialEndSec) {
      anchoredConsideringDb = currentPeriodEnd === existingTrialEndSec;
    }
    console.log("🔍 Anchor check:", { stillAnchoredToTrial, anchoredConsideringDb, currentPeriodEnd, trialEnd, existingTrialEndSec });
  } catch (error) {
    console.log("⚠️ DB anchor check failed:", error);
  }

  // If neither trialing nor anchored to trial end, no change
  if (sub.status !== 'trialing' && !anchoredConsideringDb) {
    console.log("ℹ️ Subscription not trialing and not anchored to trial end, no changes needed");
    return { changed: false, subscription: sub };
  }

  // Idempotency: skip if we've already forced adjustments
  const alreadyForced = (sub.metadata as any)?.trial_forced_end === '1';
  if (!anchoredConsideringDb && alreadyForced) {
    console.log("ℹ️ Trial already force-ended, skipping");
    return { changed: false, subscription: sub };
  }

  console.log("🔄 Adjusting billing to NOW (force-end trial if applicable)...");

  // Build update params
  const updateParams: any = {
    billing_cycle_anchor: 'now',
    proration_behavior: 'none',
    cancel_at_period_end: false,
    metadata: { ...(sub.metadata ?? {}), trial_forced_end: '1' },
  };
  if (sub.status === 'trialing' || (trialEnd && trialEnd * 1000 > Date.now())) {
    updateParams.trial_end = 'now';
  }

  await stripe.subscriptions.update(subscriptionId, updateParams);

  // Re-fetch with expansions to get accurate period dates, allow brief retry if fields lag
  let updated: any = null;
  for (let i = 0; i < 5; i++) {
    const fetched = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['customer', 'items.data.price', 'latest_invoice']
    });
    const cps = (fetched as any).current_period_start;
    const cpe = (fetched as any).current_period_end;
    const havePeriods = typeof cps === 'number' && typeof cpe === 'number' && cps > 0 && cpe > 0;
    if (havePeriods || i === 4) {
      updated = fetched;
      console.log(`🔄 Re-fetch attempt ${i + 1}:`, { havePeriods, cps, cpe });
      break;
    }
    console.log(`⏳ Waiting for period fields (attempt ${i + 1})...`);
    await new Promise((r) => setTimeout(r, 300));
  }

  console.log("✅ Billing anchor adjusted, new status:", updated.status);
  const cps = (updated as any).current_period_start;
  const cpe = (updated as any).current_period_end;
  console.log("📋 New period:", {
    start: typeof cps === 'number' ? new Date(cps * 1000).toISOString() : null,
    end: typeof cpe === 'number' ? new Date(cpe * 1000).toISOString() : null,
  });

  return { changed: true, subscription: updated };
}

/**
 * Test helper: create a trial subscription and simulate early payment via our handler
 * Returns before/after period dates from Stripe and Convex
 */
export const testTrialToPaidRealignment = internalAction({
  args: {},
  handler: async (ctx) => {
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
      return { success: false, error: 'Missing STRIPE_SECRET_KEY' };
    }
    const stripe = new Stripe(stripeSecretKey);

    // 1) Create test product+price (monthly NOK 10)
    const product = await stripe.products.create({ name: 'JobbLogg Test Plan (temp)' });
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: 1000,
      currency: 'nok',
      recurring: { interval: 'month' },
    });

    // 2) Create customer and attach test card
    const customer = await stripe.customers.create({ email: `test_${Date.now()}@example.com` });
    const pm = await stripe.paymentMethods.create({ type: 'card', card: { token: 'tok_visa' } });
    await stripe.paymentMethods.attach(pm.id, { customer: customer.id });
    await stripe.customers.update(customer.id, { invoice_settings: { default_payment_method: pm.id } });

    // 3) Create a subscription with a 7-day trial
    const trialEndSec = Math.floor(Date.now() / 1000) + 7 * 24 * 3600;
    const sub = await stripe.subscriptions.create({
      customer: customer.id,
      items: [{ price: price.id }],
      trial_end: trialEndSec,
      expand: ['customer', 'items.data.price', 'latest_invoice'],
      payment_behavior: 'default_incomplete',
      collection_method: 'charge_automatically',
    });

    const before = {
      status: sub.status,
      trial_end: (sub as any).trial_end,
      current_period_end: (sub as any).current_period_end,
    };

    // 4) Pay the latest invoice to simulate a real successful early payment
    const latestInvoiceId = typeof (sub as any).latest_invoice === 'string'
      ? (sub as any).latest_invoice
      : (sub as any).latest_invoice?.id;
    if (latestInvoiceId) {
      const inv = await stripe.invoices.retrieve(latestInvoiceId);
      const alreadyPaid = (inv as any)?.paid === true || (inv as any)?.status === 'paid';
      if (!alreadyPaid) {
        await stripe.invoices.pay(latestInvoiceId);
      }
    }

    // 5) Simulate webhook handler after payment
    const fakeEvent: any = { data: { object: { subscription: sub.id } } };
    await handleInvoicePaymentSucceeded(ctx, stripe, fakeEvent);

    // 6) Fetch updated subscription and DB record
    const updated = await stripe.subscriptions.retrieve(sub.id, { expand: ['latest_invoice'] });

    // Query Convex DB for the subscription record
    const dbInfo = await ctx.runQuery(
      internal.webhookDebug.checkSubscriptionWebhooks,
      { stripeSubscriptionId: sub.id }
    );

    const convexSub = dbInfo?.subscription;

    return {
      success: true,
      stripe: {
        before,
        after: {
          status: updated.status,
          trial_end: (updated as any).trial_end,
          current_period_end: (updated as any).current_period_end,
        },
      },
      convex: {
        currentPeriodEnd: convexSub?.currentPeriodEnd,
        trialEnd: convexSub?.trialEnd,
        status: convexSub?.status,
      },
    };
  }
});


function extractPlanLevel(subscription: Stripe.Subscription): "basic" | "professional" | "enterprise" {
  const price = subscription.items.data[0]?.price;
  const raw = (
    (price as any)?.metadata?.plan_level ??
    price?.lookup_key ??
    (price as any)?.nickname ??
    ""
  )
    .toString()
    .toLowerCase()
    .trim();

  // normalize: keep letters as words separated by underscore
  const normalized = raw.replace(/[^a-z]+/g, "_").replace(/^_+|_+$/g, "");

  const MAP: Record<string, "basic" | "professional" | "enterprise"> = {
    // English
    basic: "basic",
    professional: "professional",
    enterprise: "enterprise",
    // Norwegian variants
    liten_bedrift: "basic",
    mellomstor_bedrift: "professional",
    stor_bedrift: "enterprise",
    // Extra aliases
    small_business: "basic",
    medium_business: "professional",
    large_business: "enterprise",
  };

  return MAP[normalized] ?? "basic";
}

function extractBillingInterval(subscription: Stripe.Subscription): string {
  const interval = subscription.items.data[0]?.price?.recurring?.interval;
  return interval || 'month';
}