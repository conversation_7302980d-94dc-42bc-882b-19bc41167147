// Stripe configuration for JobbLogg
// Norwegian pricing and localization settings

export const STRIPE_CONFIG = {
  // Products and prices - Environment-aware configuration
  products: {
    // Test mode prices for development (tax-exclusive pricing)
    test: {
      basic: {
        monthly: "price_1S52rLRqXwHRnsDwTmkPIZzS", // 299 NOK/month + 25% VAT = 373.75 NOK
        yearly: "price_1S53FbRqXwHRnsDwrjI7EPU3"   // 2870 NOK/year + 25% VAT = 3587.50 NOK (20% discount)
      },
      professional: {
        monthly: "price_1S52tDRqXwHRnsDwXs8ye8ho", // 999 NOK/month + 25% VAT = 1248.75 NOK
        yearly: "price_1S53FhRqXwHRnsDwXGfR4YqM"   // 9590 NOK/year + 25% VAT = 11987.50 NOK (20% discount)
      },
      enterprise: {
        monthly: "price_1S52xTRqXwHRnsDwMcA6Er3k", // 2999 NOK/month + 25% VAT = 3748.75 NOK
        yearly: "price_1S53FoRqXwHRnsDwh6T361Fu"   // 28790 NOK/year + 25% VAT = 35987.50 NOK (20% discount)
      }
    },
    // Live mode prices for production (tax-exclusive pricing)
    live: {
      basic: {
        monthly: "price_1S3XARRqXwHRnsDwWeHWR1uT", // 299 NOK/month + 25% VAT = 373.75 NOK
        yearly: "price_1S3XASRqXwHRnsDwl4hu21hw"   // 2870 NOK/year + 25% VAT = 3587.50 NOK (20% discount) - NEEDS UPDATE
      },
      professional: {
        monthly: "price_1S3XASRqXwHRnsDwwS56xVk1", // 999 NOK/month + 25% VAT = 1248.75 NOK
        yearly: "price_1S3XASRqXwHRnsDw8tqj4oIS"   // 9590 NOK/year + 25% VAT = 11987.50 NOK (20% discount) - NEEDS UPDATE
      },
      enterprise: {
        monthly: "price_1S3XATRqXwHRnsDwiB9ljlXl", // 2999 NOK/month + 25% VAT = 3748.75 NOK
        yearly: "price_1S3XATRqXwHRnsDwKmlyOC1e"   // 28790 NOK/year + 25% VAT = 35987.50 NOK (20% discount) - NEEDS UPDATE
      }
    }
  },
  
  // Customer Portal configuration
  portalConfiguration: {
    business_profile: {
      headline: "Administrer ditt JobbLogg-abonnement",
    },
    features: {
      payment_method_update: { enabled: true },
      invoice_history: { enabled: true },
      subscription_update: {
        enabled: true,
        default_allowed_updates: ["price", "promotion_code"],
        proration_behavior: "create_prorations"
      },
      subscription_cancel: {
        enabled: true,
        mode: "at_period_end",
        proration_behavior: "none"
      }
    },
    default_return_url: process.env.CONVEX_SITE_URL + "/dashboard"
  },
  
  // Checkout configuration
  checkoutDefaults: {
    mode: "subscription" as const,
    payment_method_types: ["card"] as const,
    allow_promotion_codes: true,
    billing_address_collection: "required" as const,
    tax_id_collection: { enabled: true },
    locale: "nb" as const,
    subscription_data: {
      trial_period_days: 7,
    }
  }
} as const;

// Plan limits and pricing (Hard Limit Approach) - Tax-exclusive pricing (VAT added on top)
export const PLAN_LIMITS = {
  basic: {
    name: "Liten bedrift",
    maxSeats: 9,
    employeeRange: "1–9 ansatte",
    monthlyPrice: 299,      // 299 NOK/month + 25% VAT = 373.75 NOK total
    yearlyPrice: 2870       // 2870 NOK/year + 25% VAT = 3587.50 NOK total (20% discount)
  },
  professional: {
    name: "Mellomstor bedrift",
    maxSeats: 49,
    employeeRange: "10–49 ansatte",
    monthlyPrice: 999,      // 999 NOK/month + 25% VAT = 1248.75 NOK total
    yearlyPrice: 9590       // 9590 NOK/year + 25% VAT = 11987.50 NOK total (20% discount)
  },
  enterprise: {
    name: "Stor bedrift",
    maxSeats: 249,
    employeeRange: "50–249 ansatte",
    monthlyPrice: 2999,     // 2999 NOK/month + 25% VAT = 3748.75 NOK total
    yearlyPrice: 28790      // 28790 NOK/year + 25% VAT = 35987.50 NOK total (20% discount)
  }
} as const;

// Hard Limit Enforcement
export const SEAT_ENFORCEMENT = {
  ENFORCEMENT_MODE: "hard_limit" as const, // Block at limit
  WARNING_THRESHOLD: 0.8, // Warn at 80% capacity (7/9 seats)
  CRITICAL_THRESHOLD: 0.9, // Critical warning at 90% capacity (8/9 seats)
} as const;

// Stripe webhook events we need to handle
export const REQUIRED_WEBHOOK_EVENTS = [
  "customer.created",
  "checkout.session.completed",
  "customer.subscription.created",
  "customer.subscription.updated",
  "customer.subscription.deleted",
  "customer.subscription.trial_will_end",
  "invoice.paid",
  "invoice.payment_failed",
  "payment_intent.succeeded",
  "payment_intent.payment_failed"
] as const;

// Norwegian localization strings
export const NORWEGIAN_STRINGS = {
  trialReminders: {
    day3: {
      subject: "⏰ 4 dager igjen av gratis prøveperioden",
      message: "Du har 4 dager igjen av din gratis prøveperiode på JobbLogg."
    },
    day5: {
      subject: "⚠️ 2 dager igjen av gratis prøveperioden", 
      message: "Du har kun 2 dager igjen av din gratis prøveperiode på JobbLogg."
    },
    day24h: {
      subject: "🚨 24 timer igjen av gratis prøveperioden",
      message: "Din gratis prøveperiode utløper i morgen. Oppgrader nå for å fortsette."
    }
  },
  seatNotifications: {
    approaching: "Du nærmer deg plangrensen",
    critical: "Kun få plasser igjen på din plan",
    reached: "Plangrense nådd - oppgrader for å legge til flere"
  }
} as const;

// Helper function to get environment-appropriate price IDs
export function getStripePrices() {
  // Determine if we're in test mode based on the secret key
  const secretKey = process.env.STRIPE_SECRET_KEY;
  const isTestMode = !secretKey || secretKey.startsWith('sk_test_') || secretKey === 'sk_test_your_secret_key_here';

  return isTestMode ? STRIPE_CONFIG.products.test : STRIPE_CONFIG.products.live;
}

// Helper function to get a specific price ID
export function getPriceId(
  planLevel: 'basic' | 'professional' | 'enterprise',
  billingInterval: 'monthly' | 'yearly'
): string {
  const prices = getStripePrices();
  return prices[planLevel][billingInterval];
}
