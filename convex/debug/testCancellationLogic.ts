import { action } from "../_generated/server";
import { v } from "convex/values";
import { internal } from "../_generated/api";

/**
 * Debug action to test the new cancellation logic
 * This manually triggers the subscription status update process
 */
export const testCancellationLogic = action({
  args: {
    userId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log(`🧪 Testing cancellation logic...`);

    try {
      const now = Date.now();

      // First, let's see what subscriptions need updating
      const subscriptionsToUpdate = await ctx.runQuery(internal.subscriptions.getSubscriptionsNeedingStatusUpdate, {
        currentTime: now
      });

      console.log(`📋 Found ${subscriptionsToUpdate.length} subscriptions that need status updates:`);
      
      for (const sub of subscriptionsToUpdate) {
        console.log(`  - Subscription ${sub._id}:`);
        console.log(`    Status: ${sub.status}`);
        console.log(`    cancelAtPeriodEnd: ${sub.cancelAtPeriodEnd}`);
        console.log(`    currentPeriodEnd: ${sub.currentPeriodEnd ? new Date(sub.currentPeriodEnd).toISOString() : 'null'}`);
        console.log(`    trialEnd: ${sub.trialEnd ? new Date(sub.trialEnd).toISOString() : 'null'}`);
        console.log(`    userId: ${sub.userId}`);
      }

      // Now run the actual update process
      console.log(`🔄 Running subscription status updates...`);
      const result = await ctx.runAction(internal.subscriptions.updateSubscriptionStatuses, {});

      console.log(`✅ Update process completed. Updated ${result.updatedCount} subscriptions.`);

      return {
        success: true,
        subscriptionsFound: subscriptionsToUpdate.length,
        subscriptionsUpdated: result.updatedCount,
        subscriptions: subscriptionsToUpdate.map(sub => ({
          id: sub._id,
          status: sub.status,
          cancelAtPeriodEnd: sub.cancelAtPeriodEnd,
          currentPeriodEnd: sub.currentPeriodEnd ? new Date(sub.currentPeriodEnd).toISOString() : null,
          trialEnd: sub.trialEnd ? new Date(sub.trialEnd).toISOString() : null,
          userId: sub.userId
        }))
      };

    } catch (error) {
      console.error(`❌ Error testing cancellation logic:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  },
});

/**
 * Debug action to check specific subscription status
 */
export const checkSubscriptionStatus = action({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    console.log(`🔍 Checking subscription status for user: ${args.userId}`);

    try {
      // Get user's subscription
      const subscription = await ctx.runQuery(internal.subscriptions.getUserSubscriptionInternal, {
        userId: args.userId
      });

      if (!subscription) {
        return {
          success: false,
          error: "No subscription found for user"
        };
      }

      const now = Date.now();
      const shouldBeCanceled = subscription.cancelAtPeriodEnd && 
                              subscription.currentPeriodEnd && 
                              subscription.currentPeriodEnd < now;

      console.log(`📋 Subscription details:`);
      console.log(`  ID: ${subscription._id}`);
      console.log(`  Status: ${subscription.status}`);
      console.log(`  cancelAtPeriodEnd: ${subscription.cancelAtPeriodEnd}`);
      console.log(`  currentPeriodEnd: ${subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd).toISOString() : 'null'}`);
      console.log(`  Current time: ${new Date(now).toISOString()}`);
      console.log(`  Should be canceled: ${shouldBeCanceled}`);

      return {
        success: true,
        subscription: {
          id: subscription._id,
          status: subscription.status,
          cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
          currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd).toISOString() : null,
          shouldBeCanceled,
          currentTime: new Date(now).toISOString()
        }
      };

    } catch (error) {
      console.error(`❌ Error checking subscription status:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  },
});
