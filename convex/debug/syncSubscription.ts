import { action } from "../_generated/server";
import { v } from "convex/values";
import <PERSON><PERSON> from "stripe";

/**
 * Debug action to manually sync subscription data from Stripe
 * This can be used to force-update subscription data when webhooks fail
 */
export const syncSubscriptionFromStripe = action({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Manual subscription sync requested for user: ${args.userId}`);

    try {
      // Initialize Stripe
      const secretKey = process.env.STRIPE_SECRET_KEY;
      if (!secretKey || secretKey === 'sk_test_your_secret_key_here') {
        throw new Error("Stripe secret key not configured");
      }
      const stripe = new Stripe(secretKey, {
        apiVersion: "2025-07-30.basil",
      });

      // Find the user's subscription in our database
      const subscription = await ctx.runQuery("subscriptions:getUserSubscription", {
        userId: args.userId
      });

      if (!subscription) {
        return {
          success: false,
          error: "No subscription found for user"
        };
      }

      console.log(`📋 Found subscription in database:`, {
        id: subscription._id,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        status: subscription.status
      });

      // Fetch current subscription data from Stripe
      const stripeSubscription = await stripe.subscriptions.retrieve(
        subscription.stripeSubscriptionId,
        {
          expand: ['customer', 'items.data.price']
        }
      );

      console.log(`📋 Current Stripe subscription data:`, {
        id: stripeSubscription.id,
        status: stripeSubscription.status,
        cancel_at_period_end: stripeSubscription.cancel_at_period_end,
        cancel_at: stripeSubscription.cancel_at,
        canceled_at: stripeSubscription.canceled_at,
        current_period_end: stripeSubscription.current_period_end
      });

      // Check if there are differences
      const differences = {
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd !== stripeSubscription.cancel_at_period_end,
        status: subscription.status !== stripeSubscription.status,
      };

      console.log(`🔍 Detected differences:`, differences);

      if (differences.cancelAtPeriodEnd || differences.status) {
        // Update the subscription in our database
        const updateData = {
          status: stripeSubscription.status,
          cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
          cancelAt: stripeSubscription.cancel_at ? stripeSubscription.cancel_at * 1000 : undefined,
          canceledAt: stripeSubscription.canceled_at ? stripeSubscription.canceled_at * 1000 : undefined,
          currentPeriodStart: stripeSubscription.current_period_start * 1000,
          currentPeriodEnd: stripeSubscription.current_period_end * 1000,
          updatedAt: Date.now(),
          manualSyncAt: Date.now(), // Track that this was manually synced
        };

        await ctx.runMutation("subscriptions:updateSubscription", {
          subscriptionId: subscription._id,
          updateData
        });

        console.log(`✅ Subscription updated with Stripe data`);

        return {
          success: true,
          message: "Subscription synced successfully",
          changes: {
            cancelAtPeriodEnd: {
              before: subscription.cancelAtPeriodEnd,
              after: stripeSubscription.cancel_at_period_end
            },
            status: {
              before: subscription.status,
              after: stripeSubscription.status
            }
          }
        };
      } else {
        console.log(`ℹ️ No differences found - subscription is already in sync`);
        return {
          success: true,
          message: "Subscription is already in sync",
          changes: null
        };
      }

    } catch (error) {
      console.error(`❌ Error syncing subscription:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  },
});
