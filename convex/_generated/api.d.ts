/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as adminUtils_fixSubscriptionPeriods from "../adminUtils/fixSubscriptionPeriods.js";
import type * as adminUtils from "../adminUtils.js";
import type * as billingPortal from "../billingPortal.js";
import type * as clearAllProjectData from "../clearAllProjectData.js";
import type * as collaborationHistory from "../collaborationHistory.js";
import type * as contractorCompany from "../contractorCompany.js";
import type * as contractorOnboarding from "../contractorOnboarding.js";
import type * as contractorOnboardingSafe from "../contractorOnboardingSafe.js";
import type * as crons from "../crons.js";
import type * as customers from "../customers.js";
import type * as debug_syncSubscription from "../debug/syncSubscription.js";
import type * as debug_testCancellationLogic from "../debug/testCancellationLogic.js";
import type * as debugSubscriptionState from "../debugSubscriptionState.js";
import type * as directFix from "../directFix.js";
import type * as emailTemplates from "../emailTemplates.js";
import type * as emailTracking from "../emailTracking.js";
import type * as emails_subscriptionEmails from "../emails/subscriptionEmails.js";
import type * as emails from "../emails.js";
import type * as http from "../http.js";
import type * as imageLikes from "../imageLikes.js";
import type * as linkPreviews from "../linkPreviews.js";
import type * as logEntries from "../logEntries.js";
import type * as maintenance from "../maintenance.js";
import type * as messages from "../messages.js";
import type * as migrations_fixSubscriptionPlans from "../migrations/fixSubscriptionPlans.js";
import type * as migrations_migrateCustomerTypes from "../migrations/migrateCustomerTypes.js";
import type * as migrations_migratePersonalNotes from "../migrations/migratePersonalNotes.js";
import type * as migrations from "../migrations.js";
import type * as monitoring from "../monitoring.js";
import type * as notifications from "../notifications.js";
import type * as paymentNotifications from "../paymentNotifications.js";
import type * as planChanges from "../planChanges.js";
import type * as projects from "../projects.js";
import type * as projectsTeam from "../projectsTeam.js";
import type * as quickFix from "../quickFix.js";
import type * as resetDatabase from "../resetDatabase.js";
import type * as seatManagement from "../seatManagement.js";
import type * as simpleTest from "../simpleTest.js";
import type * as stripe_config from "../stripe/config.js";
import type * as stripe_fulfill from "../stripe/fulfill.js";
import type * as stripe_webhooks from "../stripe/webhooks.js";
import type * as subcontractorInvitations from "../subcontractorInvitations.js";
import type * as subscriptionCancellation from "../subscriptionCancellation.js";
import type * as subscriptionDowngrade from "../subscriptionDowngrade.js";
import type * as subscriptionReactivation from "../subscriptionReactivation.js";
import type * as subscriptions_upsertFromStripe from "../subscriptions/upsertFromStripe.js";
import type * as subscriptions from "../subscriptions.js";
import type * as teamManagement from "../teamManagement.js";
import type * as testDataUtilities from "../testDataUtilities.js";
import type * as trialManagement from "../trialManagement.js";
import type * as userActivity from "../userActivity.js";
import type * as userProjectNotes from "../userProjectNotes.js";
import type * as verifyFix from "../verifyFix.js";
import type * as webhookDebug from "../webhookDebug.js";
import type * as webhookMonitoring from "../webhookMonitoring.js";
import type * as webhookVerification from "../webhookVerification.js";
import type * as webhooks from "../webhooks.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "adminUtils/fixSubscriptionPeriods": typeof adminUtils_fixSubscriptionPeriods;
  adminUtils: typeof adminUtils;
  billingPortal: typeof billingPortal;
  clearAllProjectData: typeof clearAllProjectData;
  collaborationHistory: typeof collaborationHistory;
  contractorCompany: typeof contractorCompany;
  contractorOnboarding: typeof contractorOnboarding;
  contractorOnboardingSafe: typeof contractorOnboardingSafe;
  crons: typeof crons;
  customers: typeof customers;
  "debug/syncSubscription": typeof debug_syncSubscription;
  "debug/testCancellationLogic": typeof debug_testCancellationLogic;
  debugSubscriptionState: typeof debugSubscriptionState;
  directFix: typeof directFix;
  emailTemplates: typeof emailTemplates;
  emailTracking: typeof emailTracking;
  "emails/subscriptionEmails": typeof emails_subscriptionEmails;
  emails: typeof emails;
  http: typeof http;
  imageLikes: typeof imageLikes;
  linkPreviews: typeof linkPreviews;
  logEntries: typeof logEntries;
  maintenance: typeof maintenance;
  messages: typeof messages;
  "migrations/fixSubscriptionPlans": typeof migrations_fixSubscriptionPlans;
  "migrations/migrateCustomerTypes": typeof migrations_migrateCustomerTypes;
  "migrations/migratePersonalNotes": typeof migrations_migratePersonalNotes;
  migrations: typeof migrations;
  monitoring: typeof monitoring;
  notifications: typeof notifications;
  paymentNotifications: typeof paymentNotifications;
  planChanges: typeof planChanges;
  projects: typeof projects;
  projectsTeam: typeof projectsTeam;
  quickFix: typeof quickFix;
  resetDatabase: typeof resetDatabase;
  seatManagement: typeof seatManagement;
  simpleTest: typeof simpleTest;
  "stripe/config": typeof stripe_config;
  "stripe/fulfill": typeof stripe_fulfill;
  "stripe/webhooks": typeof stripe_webhooks;
  subcontractorInvitations: typeof subcontractorInvitations;
  subscriptionCancellation: typeof subscriptionCancellation;
  subscriptionDowngrade: typeof subscriptionDowngrade;
  subscriptionReactivation: typeof subscriptionReactivation;
  "subscriptions/upsertFromStripe": typeof subscriptions_upsertFromStripe;
  subscriptions: typeof subscriptions;
  teamManagement: typeof teamManagement;
  testDataUtilities: typeof testDataUtilities;
  trialManagement: typeof trialManagement;
  userActivity: typeof userActivity;
  userProjectNotes: typeof userProjectNotes;
  verifyFix: typeof verifyFix;
  webhookDebug: typeof webhookDebug;
  webhookMonitoring: typeof webhookMonitoring;
  webhookVerification: typeof webhookVerification;
  webhooks: typeof webhooks;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
