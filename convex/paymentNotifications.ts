/**
 * Payment Failure Notification System
 * 
 * Handles sending Norwegian-localized email notifications for payment failures,
 * integrating with dunning management and payment retry systems.
 */

import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalAction, internalMutation, internalQuery, mutation, query } from "./_generated/server";

// ===== PAYMENT NOTIFICATION CONFIGURATION =====

/**
 * Initialize payment notification preferences for a user
 */
export const initializePaymentNotificationPreferences = mutation({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Check if preferences already exist
    const existingPrefs = await ctx.db
      .query("paymentNotificationPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (existingPrefs) {
      return existingPrefs;
    }

    // Create default preferences
    const preferences = {
      userId: args.userId,
      emailNotifications: true,
      smsNotifications: false, // Future feature
      notificationTypes: {
        paymentFailed: true,
        retryAttempts: false, // Don't spam with retry notifications
        retryExhausted: true,
        subscriptionSuspended: true,
        paymentSucceeded: true,
      },
      frequency: "immediate" as const, // immediate, daily_digest, weekly_digest
      lastNotificationSent: undefined,
      unsubscribeToken: generateUnsubscribeToken(),
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const prefsId = await ctx.db.insert("paymentNotificationPreferences", preferences);
    return { ...preferences, _id: prefsId };
  },
});

/**
 * Send payment failure notification
 */
export const sendPaymentFailureNotification = internalAction({
  args: {
    userId: v.string(),
    subscriptionId: v.id("subscriptions"),
    failureType: v.union(
      v.literal("card_declined"),
      v.literal("insufficient_funds"),
      v.literal("expired_card"),
      v.literal("authentication_required"),
      v.literal("processing_error"),
      v.literal("generic_decline"),
      v.literal("unknown")
    ),
    failureReason: v.string(),
    declineCode: v.optional(v.string()),
    attemptNumber: v.optional(v.number()),
    maxAttempts: v.optional(v.number()),
    nextRetryAt: v.optional(v.number()),
    amount: v.optional(v.number()),
    currency: v.optional(v.string()),
    lastFourDigits: v.optional(v.string()),
    invoiceUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log(`📧 Sending payment failure notification to user: ${args.userId}`);

    try {
      // Get user details
      const user = await ctx.runQuery(internal.users.getUserByClerkId, {
        clerkUserId: args.userId,
      });

      if (!user || !user.email) {
        console.error(`❌ User not found or no email: ${args.userId}`);
        return { success: false, error: 'User not found or no email' };
      }

      // Get notification preferences
      const preferences = await ctx.runQuery(internal.paymentNotifications.getNotificationPreferences, {
        userId: args.userId,
      });

      if (!preferences?.emailNotifications || !preferences.notificationTypes.paymentFailed) {
        console.log(`⏭️ User has disabled payment failure notifications: ${args.userId}`);
        return { success: true, skipped: true, reason: 'notifications_disabled' };
      }

      // Check notification timing to avoid spam
      const shouldSend = await ctx.runMutation(internal.paymentNotifications.checkNotificationTiming, {
        userId: args.userId,
        notificationType: 'payment_failed',
      });

      if (!shouldSend) {
        console.log(`⏭️ Notification timing check failed for user: ${args.userId}`);
        return { success: true, skipped: true, reason: 'timing_check_failed' };
      }

      // Get subscription details
      const subscription = await ctx.runQuery(internal.subscriptions.getSubscriptionByUserInternal, {
        userId: args.userId,
      });

      if (!subscription) {
        console.error(`❌ Subscription not found: ${args.subscriptionId}`);
        return { success: false, error: 'Subscription not found' };
      }

      // Determine email template type based on failure type
      const emailType = getEmailTypeForFailure(args.failureType);

      // Format amount for display
      const formattedAmount = args.amount && args.currency 
        ? `${(args.amount / 100).toFixed(0)} ${args.currency.toUpperCase()}`
        : undefined;

      // Create billing portal URL
      const billingPortalUrl = await createBillingPortalUrl(ctx, user.stripeCustomerId);

      // Prepare email metadata
      const emailMetadata = {
        planName: subscription.planLevel,
        amount: formattedAmount,
        failureReason: args.failureReason,
        declineCode: args.declineCode,
        attemptNumber: args.attemptNumber,
        maxAttempts: args.maxAttempts,
        nextRetryAt: args.nextRetryAt,
        billingPortalUrl,
        invoiceUrl: args.invoiceUrl,
        lastFourDigits: args.lastFourDigits,
      };

      // Send email notification
      const emailResult = await ctx.runMutation(internal.emails.subscriptionEmails.sendSubscriptionEmail, {
        userId: args.userId,
        type: emailType,
        userEmail: user.email,
        userName: user.name || user.email,
        metadata: emailMetadata,
      });

      // Record notification sent
      await ctx.runMutation(internal.paymentNotifications.recordNotificationSent, {
        userId: args.userId,
        notificationType: 'payment_failed',
        failureType: args.failureType,
        emailResult,
      });

      console.log(`✅ Payment failure notification sent to: ${user.email}`);
      return { success: true, emailResult };

    } catch (error) {
      console.error(`❌ Error sending payment failure notification:`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  },
});

/**
 * Send payment retry exhausted notification
 */
export const sendPaymentRetryExhaustedNotification = internalAction({
  args: {
    userId: v.string(),
    subscriptionId: v.id("subscriptions"),
    totalAttempts: v.number(),
    lastFailureReason: v.string(),
    amount: v.optional(v.number()),
    currency: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log(`📧 Sending payment retry exhausted notification to user: ${args.userId}`);

    try {
      // Get user details
      const user = await ctx.runQuery(internal.users.getUserByClerkId, {
        clerkUserId: args.userId,
      });

      if (!user || !user.email) {
        console.error(`❌ User not found or no email: ${args.userId}`);
        return { success: false, error: 'User not found or no email' };
      }

      // Get notification preferences
      const preferences = await ctx.runQuery(internal.paymentNotifications.getNotificationPreferences, {
        userId: args.userId,
      });

      if (!preferences?.emailNotifications || !preferences.notificationTypes.retryExhausted) {
        console.log(`⏭️ User has disabled retry exhausted notifications: ${args.userId}`);
        return { success: true, skipped: true, reason: 'notifications_disabled' };
      }

      // Get subscription details
      const subscription = await ctx.runQuery(internal.subscriptions.getSubscriptionByUserInternal, {
        userId: args.userId,
      });

      if (!subscription) {
        console.error(`❌ Subscription not found: ${args.subscriptionId}`);
        return { success: false, error: 'Subscription not found' };
      }

      // Format amount for display
      const formattedAmount = args.amount && args.currency 
        ? `${(args.amount / 100).toFixed(0)} ${args.currency.toUpperCase()}`
        : undefined;

      // Create billing portal URL
      const billingPortalUrl = await createBillingPortalUrl(ctx, user.stripeCustomerId);

      // Prepare email metadata
      const emailMetadata = {
        planName: subscription.planLevel,
        amount: formattedAmount,
        attemptNumber: args.totalAttempts,
        failureReason: args.lastFailureReason,
        billingPortalUrl,
      };

      // Send email notification
      const emailResult = await ctx.runMutation(internal.emails.subscriptionEmails.sendSubscriptionEmail, {
        userId: args.userId,
        type: 'payment_retry_exhausted',
        userEmail: user.email,
        userName: user.name || user.email,
        metadata: emailMetadata,
      });

      // Record notification sent
      await ctx.runMutation(internal.paymentNotifications.recordNotificationSent, {
        userId: args.userId,
        notificationType: 'retry_exhausted',
        emailResult,
      });

      console.log(`✅ Payment retry exhausted notification sent to: ${user.email}`);
      return { success: true, emailResult };

    } catch (error) {
      console.error(`❌ Error sending retry exhausted notification:`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  },
});

/**
 * Send subscription suspension notification
 */
export const sendSubscriptionSuspensionNotification = internalAction({
  args: {
    userId: v.string(),
    subscriptionId: v.id("subscriptions"),
    reason: v.string(),
    gracePeriodEnd: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    console.log(`📧 Sending subscription suspension notification to user: ${args.userId}`);

    try {
      // Get user details
      const user = await ctx.runQuery(internal.users.getUserByClerkId, {
        clerkUserId: args.userId,
      });

      if (!user || !user.email) {
        console.error(`❌ User not found or no email: ${args.userId}`);
        return { success: false, error: 'User not found or no email' };
      }

      // Get notification preferences
      const preferences = await ctx.runQuery(internal.paymentNotifications.getNotificationPreferences, {
        userId: args.userId,
      });

      if (!preferences?.emailNotifications || !preferences.notificationTypes.subscriptionSuspended) {
        console.log(`⏭️ User has disabled suspension notifications: ${args.userId}`);
        return { success: true, skipped: true, reason: 'notifications_disabled' };
      }

      // Get subscription details
      const subscription = await ctx.runQuery(internal.subscriptions.getSubscriptionByUserInternal, {
        userId: args.userId,
      });

      if (!subscription) {
        console.error(`❌ Subscription not found: ${args.subscriptionId}`);
        return { success: false, error: 'Subscription not found' };
      }

      // Create billing portal URL
      const billingPortalUrl = await createBillingPortalUrl(ctx, user.stripeCustomerId);

      // Calculate price based on plan level and billing interval
      const planPrices = {
        basic: { month: 299, year: 2870 },
        professional: { month: 999, year: 9590 },
        enterprise: { month: 2999, year: 28790 }
      };

      const planLevel = subscription.planLevel as keyof typeof planPrices;
      const billingInterval = subscription.billingInterval as 'month' | 'year';
      const price = planPrices[planLevel]?.[billingInterval] || 299;

      // Prepare email metadata
      const emailMetadata = {
        planName: subscription.planLevel,
        billingPortalUrl,
        amount: `${price} NOK`,
      };

      // Send email notification
      const emailResult = await ctx.runMutation(internal.emails.subscriptionEmails.sendSubscriptionEmail, {
        userId: args.userId,
        type: 'subscription_suspended',
        userEmail: user.email,
        userName: user.name || user.email,
        metadata: emailMetadata,
      });

      // Record notification sent
      await ctx.runMutation(internal.paymentNotifications.recordNotificationSent, {
        userId: args.userId,
        notificationType: 'subscription_suspended',
        emailResult,
      });

      console.log(`✅ Subscription suspension notification sent to: ${user.email}`);
      return { success: true, emailResult };

    } catch (error) {
      console.error(`❌ Error sending suspension notification:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },
});

// ===== HELPER FUNCTIONS =====

/**
 * Determine email template type based on failure type
 */
function getEmailTypeForFailure(failureType: string): string {
  switch (failureType) {
    case 'card_declined':
      return 'payment_failed_card_declined';
    case 'insufficient_funds':
      return 'payment_failed_insufficient_funds';
    case 'expired_card':
      return 'payment_failed_expired_card';
    case 'authentication_required':
      return 'payment_failed_authentication_required';
    default:
      return 'payment_failed';
  }
}

/**
 * Generate unsubscribe token
 */
function generateUnsubscribeToken(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Create Stripe billing portal URL
 */
async function createBillingPortalUrl(ctx: any, stripeCustomerId?: string): Promise<string> {
  if (!stripeCustomerId) {
    return `${process.env.CONVEX_SITE_URL || 'https://jobblogg.no'}/subscription`;
  }

  try {
    // TODO: Integrate with Stripe billing portal
    // For now, return subscription management URL
    return `${process.env.CONVEX_SITE_URL || 'https://jobblogg.no'}/subscription`;
  } catch (error) {
    console.error('Error creating billing portal URL:', error);
    return `${process.env.CONVEX_SITE_URL || 'https://jobblogg.no'}/subscription`;
  }
}

// ===== INTERNAL QUERIES AND MUTATIONS =====

/**
 * Get notification preferences for a user (internal)
 */
export const getNotificationPreferences = internalQuery({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("paymentNotificationPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();
  },
});

/**
 * Check notification timing to avoid spam (internal)
 */
export const checkNotificationTiming = internalMutation({
  args: {
    userId: v.string(),
    notificationType: v.string(),
  },
  handler: async (ctx, args) => {
    const preferences = await ctx.db
      .query("paymentNotificationPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!preferences) {
      return true; // Allow if no preferences set
    }

    // Check if we've sent a notification recently (avoid spam)
    const now = Date.now();
    const lastSent = preferences.lastNotificationSent;
    const minInterval = 2 * 60 * 60 * 1000; // 2 hours minimum between notifications

    if (lastSent && (now - lastSent) < minInterval) {
      return false; // Too soon
    }

    return true;
  },
});

/**
 * Record that a notification was sent (internal)
 */
export const recordNotificationSent = internalMutation({
  args: {
    userId: v.string(),
    notificationType: v.string(),
    failureType: v.optional(v.string()),
    emailResult: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    // Update notification preferences with last sent time
    const preferences = await ctx.db
      .query("paymentNotificationPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (preferences) {
      await ctx.db.patch(preferences._id, {
        lastNotificationSent: Date.now(),
        updatedAt: Date.now(),
      });
    }

    // Create notification history record
    await ctx.db.insert("paymentNotificationHistory", {
      userId: args.userId,
      notificationType: args.notificationType,
      failureType: args.failureType,
      emailResult: args.emailResult,
      sentAt: Date.now(),
    });
  },
});

/**
 * Get notification history for a user
 */
export const getNotificationHistory = query({
  args: {
    userId: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    return await ctx.db
      .query("paymentNotificationHistory")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);
  },
});

/**
 * Update notification preferences
 */
export const updateNotificationPreferences = mutation({
  args: {
    userId: v.string(),
    emailNotifications: v.optional(v.boolean()),
    notificationTypes: v.optional(v.object({
      paymentFailed: v.optional(v.boolean()),
      retryAttempts: v.optional(v.boolean()),
      retryExhausted: v.optional(v.boolean()),
      subscriptionSuspended: v.optional(v.boolean()),
      paymentSucceeded: v.optional(v.boolean()),
    })),
    frequency: v.optional(v.union(
      v.literal("immediate"),
      v.literal("daily_digest"),
      v.literal("weekly_digest")
    )),
  },
  handler: async (ctx, args) => {
    const preferences = await ctx.db
      .query("paymentNotificationPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!preferences) {
      throw new Error("Notification preferences not found");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.emailNotifications !== undefined) {
      updates.emailNotifications = args.emailNotifications;
    }

    if (args.notificationTypes) {
      updates.notificationTypes = {
        ...preferences.notificationTypes,
        ...args.notificationTypes,
      };
    }

    if (args.frequency) {
      updates.frequency = args.frequency;
    }

    await ctx.db.patch(preferences._id, updates);
    return await ctx.db.get(preferences._id);
  },
});

// ===== PUBLIC API FUNCTIONS =====

/**
 * Get notification preferences for the current user (public)
 */
export const getUserNotificationPreferences = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("paymentNotificationPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();
  },
});

/**
 * Initialize notification preferences for the current user (public)
 */
export const initializeUserNotificationPreferences = mutation({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // Check if preferences already exist
    const existingPrefs = await ctx.db
      .query("paymentNotificationPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (existingPrefs) {
      return existingPrefs;
    }

    // Create default preferences
    const preferences = {
      userId: args.userId,
      emailNotifications: true,
      smsNotifications: false, // Future feature
      notificationTypes: {
        paymentFailed: true,
        retryAttempts: false, // Don't spam with retry notifications
        retryExhausted: true,
        subscriptionSuspended: true,
        paymentSucceeded: true,
      },
      frequency: "immediate" as const, // immediate, daily_digest, weekly_digest
      lastNotificationSent: undefined,
      unsubscribeToken: generateUnsubscribeToken(),
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const prefsId = await ctx.db.insert("paymentNotificationPreferences", preferences);
    return { ...preferences, _id: prefsId };
  },
});

/**
 * Update notification preferences for the current user (public)
 */
export const updateUserNotificationPreferences = mutation({
  args: {
    userId: v.string(),
    emailNotifications: v.optional(v.boolean()),
    notificationTypes: v.optional(v.object({
      paymentFailed: v.optional(v.boolean()),
      retryAttempts: v.optional(v.boolean()),
      retryExhausted: v.optional(v.boolean()),
      subscriptionSuspended: v.optional(v.boolean()),
      paymentSucceeded: v.optional(v.boolean()),
    })),
    frequency: v.optional(v.union(
      v.literal("immediate"),
      v.literal("daily_digest"),
      v.literal("weekly_digest")
    )),
  },
  handler: async (ctx, args) => {
    const preferences = await ctx.db
      .query("paymentNotificationPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!preferences) {
      throw new Error("Notification preferences not found");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.emailNotifications !== undefined) {
      updates.emailNotifications = args.emailNotifications;
    }

    if (args.notificationTypes) {
      updates.notificationTypes = {
        ...preferences.notificationTypes,
        ...args.notificationTypes,
      };
    }

    if (args.frequency) {
      updates.frequency = args.frequency;
    }

    await ctx.db.patch(preferences._id, updates);
    return await ctx.db.get(preferences._id);
  },
});
