import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalAction, internalMutation, internalQuery, mutation, query } from "./_generated/server";

/**
 * Check if user can reactivate subscription
 */
export const canReactivateSubscription = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Find the most recent cancelled subscription
    const cancelledSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("status"), "canceled"))
      .order("desc")
      .first();

    if (!cancelledSubscription) {
      return { 
        canReactivate: false, 
        reason: "Ingen avbrutt abonnement funnet",
        subscription: null
      };
    }

    // Check if there's already an active subscription
    const activeSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.or(
        q.eq(q.field("status"), "active"),
        q.eq(q.field("status"), "trialing")
      ))
      .first();

    if (activeSubscription) {
      return { 
        canReactivate: false, 
        reason: "Du har allerede et aktivt abonnement",
        subscription: null
      };
    }

    // Check for pending reactivation requests
    const pendingReactivation = await ctx.db
      .query("subscriptionReactivations")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.or(
        q.eq(q.field("status"), "pending"),
        q.eq(q.field("status"), "processing")
      ))
      .first();

    if (pendingReactivation) {
      return { 
        canReactivate: false, 
        reason: "Du har allerede en pågående reaktiveringsforespørsel",
        subscription: null
      };
    }

    // Calculate data retention status
    const dataRetentionPeriod = 90 * 24 * 60 * 60 * 1000; // 90 days in milliseconds
    const cancelledAt = cancelledSubscription.canceledAt || cancelledSubscription.updatedAt;
    const dataRetentionUntil = cancelledAt + dataRetentionPeriod;
    const isDataExpired = Date.now() > dataRetentionUntil;

    return { 
      canReactivate: true,
      subscription: {
        ...cancelledSubscription,
        dataRetentionUntil: new Date(dataRetentionUntil).toISOString(),
        isDataExpired,
      }
    };
  },
});

/**
 * Get available reactivation plans
 */
export const getReactivationPlans = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const plans = [
      {
        id: 'basic',
        name: 'Basic',
        monthlyPrice: 299,
        annualPrice: 2870, // 20% discount
        description: 'Perfekt for små prosjekter og oppstartsbedrifter',
        features: [
          'Opptil 5 aktive prosjekter',
          'Grunnleggende prosjektdokumentasjon',
          'E-poststøtte',
          'Mobilapp tilgang'
        ]
      },
      {
        id: 'professional',
        name: 'Professional',
        monthlyPrice: 999,
        annualPrice: 9590, // 20% discount
        description: 'Ideell for voksende bedrifter med flere prosjekter',
        recommended: true,
        features: [
          'Ubegrensede prosjekter',
          'Avansert prosjektdokumentasjon',
          'Teamsamarbeid og tilgangskontroll',
          'Prioritert e-poststøtte',
          'Avanserte rapporter og analyser',
          'API-tilgang'
        ]
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        monthlyPrice: 2999,
        annualPrice: 28790, // 20% discount
        description: 'For store organisasjoner med komplekse behov',
        features: [
          'Alt i Professional',
          'Dedikert kundesuksessansvarlig',
          'Tilpassede integrasjoner',
          'Avansert sikkerhet og compliance',
          'Ubegrenset lagringsplass',
          'Prioritert telefonstøtte'
        ]
      }
    ];

    return plans;
  },
});

/**
 * Initiate subscription reactivation
 */
export const initiateSubscriptionReactivation = mutation({
  args: {
    userId: v.string(),
    planId: v.string(),
    billingInterval: v.union(v.literal("month"), v.literal("year")),
    paymentMethodId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate that user can reactivate
    const canReactivate = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("status"), "canceled"))
      .order("desc")
      .first();

    if (!canReactivate) {
      throw new Error("Ingen avbrutt abonnement funnet for reaktivering");
    }

    // Check for active subscriptions
    const activeSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.or(
        q.eq(q.field("status"), "active"),
        q.eq(q.field("status"), "trialing")
      ))
      .first();

    if (activeSubscription) {
      throw new Error("Du har allerede et aktivt abonnement");
    }

    // Validate plan
    const validPlans = ['basic', 'professional', 'enterprise'];
    if (!validPlans.includes(args.planId)) {
      throw new Error("Ugyldig plan valgt");
    }

    // Create reactivation request
    const reactivationId = await ctx.db.insert("subscriptionReactivations", {
      userId: args.userId,
      originalSubscriptionId: canReactivate._id,
      newPlanId: args.planId,
      newBillingInterval: args.billingInterval,
      paymentMethodId: args.paymentMethodId,
      status: "pending",
      welcomeBackDiscount: 15, // 15% discount for first billing period
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Schedule the actual reactivation processing
    await ctx.scheduler.runAfter(0, internal.subscriptionReactivation.processReactivation, {
      reactivationRequestId: reactivationId,
    });

    return {
      reactivationRequestId: reactivationId,
      status: "initiated",
      message: "Reaktivering er startet og vil bli behandlet umiddelbart",
    };
  },
});

/**
 * Process subscription reactivation with Stripe
 */
export const processReactivation = internalAction({
  args: {
    reactivationRequestId: v.id("subscriptionReactivations"),
  },
  handler: async (ctx, args) => {
    const reactivationRequest = await ctx.runQuery(internal.subscriptionReactivation.getReactivationRequest, {
      reactivationRequestId: args.reactivationRequestId,
    });

    if (!reactivationRequest) {
      throw new Error("Reactivation request not found");
    }

    if (reactivationRequest.status !== "pending") {
      throw new Error("Reactivation request is not pending");
    }

    try {
      // Update status to processing
      await ctx.runMutation(internal.subscriptionReactivation.updateReactivationStatus, {
        reactivationRequestId: args.reactivationRequestId,
        status: "processing",
      });

      // Get plan configuration
      const planConfig = {
        basic: { monthlyPrice: 299, annualPrice: 2870 },
        professional: { monthlyPrice: 999, annualPrice: 9590 },
        enterprise: { monthlyPrice: 2999, annualPrice: 28790 },
      };

      const plan = planConfig[reactivationRequest.newPlanId as keyof typeof planConfig];
      const price = reactivationRequest.newBillingInterval === 'year' ? plan.annualPrice : plan.monthlyPrice;

      // Apply welcome back discount
      const discountedPrice = Math.round(price * (1 - reactivationRequest.welcomeBackDiscount / 100));

      // Create new Stripe subscription
      const stripeResult = await ctx.runAction(internal.subscriptionReactivation.createStripeSubscription, {
        userId: reactivationRequest.userId,
        planId: reactivationRequest.newPlanId,
        billingInterval: reactivationRequest.newBillingInterval,
        discountedPrice,
        paymentMethodId: reactivationRequest.paymentMethodId,
      });

      if (!stripeResult.success) {
        throw new Error(`Stripe subscription creation failed: ${stripeResult.error}`);
      }

      // Create new subscription record
      const newSubscriptionId = await ctx.runMutation(internal.subscriptionReactivation.createNewSubscription, {
        userId: reactivationRequest.userId,
        planId: reactivationRequest.newPlanId,
        billingInterval: reactivationRequest.newBillingInterval,
        stripeData: stripeResult.subscription,
        originalSubscriptionId: reactivationRequest.originalSubscriptionId,
      });

      // Update reactivation request status
      await ctx.runMutation(internal.subscriptionReactivation.updateReactivationStatus, {
        reactivationRequestId: args.reactivationRequestId,
        status: "completed",
        newSubscriptionId,
        completedAt: Date.now(),
        stripeSubscriptionId: stripeResult.subscription.id,
      });

      // Send welcome back email
      await ctx.runAction(internal.subscriptionReactivation.sendWelcomeBackEmail, {
        userId: reactivationRequest.userId,
        reactivationRequestId: args.reactivationRequestId,
      });

      console.log(`✅ Subscription reactivation completed for user ${reactivationRequest.userId}`);

      return {
        success: true,
        reactivationRequestId: args.reactivationRequestId,
        newSubscriptionId,
        planId: reactivationRequest.newPlanId,
        billingInterval: reactivationRequest.newBillingInterval,
      };

    } catch (error) {
      console.error("❌ Subscription reactivation failed:", error);

      // Update status to failed
      await ctx.runMutation(internal.subscriptionReactivation.updateReactivationStatus, {
        reactivationRequestId: args.reactivationRequestId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        completedAt: Date.now(),
      });

      throw error;
    }
  },
});

/**
 * Create Stripe subscription for reactivation
 */
export const createStripeSubscription = internalAction({
  args: {
    userId: v.string(),
    planId: v.string(),
    billingInterval: v.union(v.literal("month"), v.literal("year")),
    discountedPrice: v.number(),
    paymentMethodId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const stripe = new (await import('stripe')).default(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: "2025-08-27.basil",
    });

    try {
      // Get or create Stripe customer
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (!user) {
        throw new Error("User not found");
      }

      let stripeCustomerId = user.stripeCustomerId;

      if (!stripeCustomerId) {
        // Create new Stripe customer
        const customer = await stripe.customers.create({
          email: user.email,
          name: user.name || user.email,
          metadata: {
            clerkUserId: args.userId,
            reactivation: 'true',
          },
        });
        stripeCustomerId = customer.id;

        // Update user with Stripe customer ID
        await ctx.db.patch(user._id, {
          stripeCustomerId: stripeCustomerId,
          updatedAt: Date.now(),
        });
      }

      // Get price ID for the plan
      const priceIds = {
        basic: {
          month: process.env.STRIPE_BASIC_MONTHLY_PRICE_ID,
          year: process.env.STRIPE_BASIC_ANNUAL_PRICE_ID,
        },
        professional: {
          month: process.env.STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID,
          year: process.env.STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID,
        },
        enterprise: {
          month: process.env.STRIPE_ENTERPRISE_MONTHLY_PRICE_ID,
          year: process.env.STRIPE_ENTERPRISE_ANNUAL_PRICE_ID,
        },
      };

      const priceId = priceIds[args.planId as keyof typeof priceIds]?.[args.billingInterval];

      if (!priceId) {
        throw new Error(`Price ID not found for ${args.planId} ${args.billingInterval}`);
      }

      // Create subscription with welcome back discount
      const subscription = await stripe.subscriptions.create({
        customer: stripeCustomerId,
        items: [{ price: priceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          clerkUserId: args.userId,
          planId: args.planId,
          billingInterval: args.billingInterval,
          reactivation: 'true',
          welcomeBackDiscount: '15',
        },
        // Apply welcome back discount for first billing period
        discounts: [{
          coupon: process.env.STRIPE_WELCOME_BACK_COUPON_ID || 'welcome-back-15',
        }],
      });

      return {
        success: true,
        subscription,
        clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
      };

    } catch (error) {
      console.error("Stripe subscription creation failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown Stripe error",
      };
    }
  },
});

// Helper functions
export const getReactivationRequest = internalQuery({
  args: {
    reactivationRequestId: v.id("subscriptionReactivations"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.reactivationRequestId);
  },
});

export const updateReactivationStatus = internalMutation({
  args: {
    reactivationRequestId: v.id("subscriptionReactivations"),
    status: v.string(),
    completedAt: v.optional(v.number()),
    errorMessage: v.optional(v.string()),
    newSubscriptionId: v.optional(v.id("subscriptions")),
    stripeSubscriptionId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.completedAt) updates.completedAt = args.completedAt;
    if (args.errorMessage) updates.errorMessage = args.errorMessage;
    if (args.newSubscriptionId) updates.newSubscriptionId = args.newSubscriptionId;
    if (args.stripeSubscriptionId) updates.stripeSubscriptionId = args.stripeSubscriptionId;

    await ctx.db.patch(args.reactivationRequestId, updates);
  },
});

export const createNewSubscription = internalMutation({
  args: {
    userId: v.string(),
    planId: v.string(),
    billingInterval: v.union(v.literal("month"), v.literal("year")),
    stripeData: v.any(),
    originalSubscriptionId: v.id("subscriptions"),
  },
  handler: async (ctx, args) => {
    const subscriptionId = await ctx.db.insert("subscriptions", {
      userId: args.userId,
      planLevel: args.planId,
      billingInterval: args.billingInterval,
      status: args.stripeData.status,
      stripeSubscriptionId: args.stripeData.id,
      stripeCustomerId: args.stripeData.customer,
      currentPeriodStart: args.stripeData.current_period_start * 1000,
      currentPeriodEnd: args.stripeData.current_period_end * 1000,
      cancelAtPeriodEnd: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Log the reactivation event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId,
      eventType: 'reactivated',
      eventData: {
        originalSubscriptionId: args.originalSubscriptionId,
        planId: args.planId,
        billingInterval: args.billingInterval,
        welcomeBackDiscount: 15,
        discountedAmount: args.discountedAmount,
        stripeSubscriptionId: args.stripeData.id,
      },
      timestamp: Date.now(),
      source: 'user_action',
    });

    return subscriptionId;
  },
});

/**
 * Send welcome back email
 */
export const sendWelcomeBackEmail = internalAction({
  args: {
    userId: v.string(),
    reactivationRequestId: v.id("subscriptionReactivations"),
  },
  handler: async (ctx, args) => {
    const reactivationRequest = await ctx.runQuery(internal.subscriptionReactivation.getReactivationRequest, {
      reactivationRequestId: args.reactivationRequestId,
    });

    if (!reactivationRequest) {
      throw new Error("Reactivation request not found");
    }

    // Get user details for email
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    const planNames = {
      basic: 'Basic',
      professional: 'Professional',
      enterprise: 'Enterprise',
    };

    const planName = planNames[reactivationRequest.newPlanId as keyof typeof planNames];
    const billingText = reactivationRequest.newBillingInterval === 'year' ? 'årlig' : 'månedlig';

    const emailContent = {
      subject: `Velkommen tilbake til JobbLogg! 🎉`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%); padding: 40px 20px; text-align: center; border-radius: 8px 8px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Velkommen tilbake! 🎉</h1>
            <p style="color: #E0E7FF; margin: 10px 0 0 0; font-size: 16px;">
              Vi er så glade for å se deg igjen på JobbLogg
            </p>
          </div>

          <div style="background-color: #f8fafc; padding: 30px 20px;">
            <h2 style="color: #2563EB; margin-top: 0;">Ditt abonnement er reaktivert!</h2>

            <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10B981;">
              <h3 style="margin-top: 0; color: #1F2937;">Abonnementsdetaljer</h3>
              <p><strong>Plan:</strong> ${planName}</p>
              <p><strong>Fakturering:</strong> ${billingText}</p>
              <p><strong>Reaktivert:</strong> ${new Date().toLocaleDateString('nb-NO')}</p>
              <p><strong>Spesialtilbud:</strong> 15% rabatt på første faktura! 🎁</p>
            </div>

            <h3 style="color: #1F2937;">Hva skjer nå?</h3>
            <ul style="color: #4B5563; line-height: 1.6;">
              <li>Du har umiddelbart tilgang til alle funksjoner i din plan</li>
              <li>Alle dine tidligere prosjektdata er tilgjengelige</li>
              <li>Du får 15% rabatt på første faktureringsperiode</li>
              <li>Teammedlemmene dine får automatisk tilgang igjen</li>
            </ul>

            <div style="background-color: #FEF3C7; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #F59E0B;">
              <h4 style="margin-top: 0; color: #92400E;">💡 Tips for å komme i gang igjen</h4>
              <ul style="color: #92400E; margin-bottom: 0;">
                <li>Sjekk at alle prosjektene dine er som forventet</li>
                <li>Inviter teammedlemmer som måtte ha mistet tilgang</li>
                <li>Utforsk nye funksjoner som er lagt til siden sist</li>
                <li>Oppdater prosjektinformasjon hvis nødvendig</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL || 'https://jobblogg.no'}/dashboard"
                 style="background-color: #2563EB; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
                Gå til dashbordet
              </a>
            </div>

            <div style="background-color: #EFF6FF; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h4 style="margin-top: 0; color: #1E40AF;">🤝 Vi er her for å hjelpe</h4>
              <p style="color: #1E40AF; margin-bottom: 0;">
                Hvis du har spørsmål eller trenger hjelp med å komme i gang igjen,
                ikke nøl med å kontakte oss på
                <a href="mailto:<EMAIL>" style="color: #2563EB;"><EMAIL></a>
              </p>
            </div>
          </div>

          <div style="background-color: #1F2937; padding: 20px; text-align: center; border-radius: 0 0 8px 8px;">
            <p style="color: #9CA3AF; margin: 0; font-size: 14px;">
              JobbLogg - Prosjektdokumentasjon for håndverkere<br>
              <a href="mailto:<EMAIL>" style="color: #60A5FA;"><EMAIL></a>
            </p>
          </div>
        </div>
      `,
    };

    await ctx.runAction(internal.emails.sendEmail, {
      to: user.email,
      subject: emailContent.subject,
      html: emailContent.html,
      recipientName: user.name || user.email,
    });

    console.log(`✅ Welcome back email sent to ${user.email}`);
  },
});

/**
 * Get user's reactivation history
 */
export const getReactivationHistory = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptionReactivations")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(10);
  },
});
