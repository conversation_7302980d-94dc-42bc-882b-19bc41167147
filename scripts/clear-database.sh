#!/bin/bash

# JobbLogg Database Clear Script
# WARNING: This will permanently delete ALL data in your Convex database!

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${RED}🚨 DANGER ZONE 🚨${NC}"
echo -e "${RED}This script will permanently delete ALL data from your Convex database!${NC}"
echo ""
echo -e "${YELLOW}Available options:${NC}"
echo "1. Clear ALL tables (complete database wipe)"
echo "2. Clear specific tables only"
echo "3. Cancel (exit safely)"
echo ""

read -p "Choose an option (1-3): " choice

case $choice in
    1)
        echo ""
        echo -e "${RED}⚠️  You are about to delete ALL data from ALL tables!${NC}"
        echo -e "${RED}This action cannot be undone!${NC}"
        echo ""
        read -p "Type 'DELETE ALL DATA' to confirm: " confirmation
        
        if [ "$confirmation" = "DELETE ALL DATA" ]; then
            echo ""
            echo -e "${BLUE}🗑️  Starting complete database clear...${NC}"
            npx convex run scripts/clearDatabase:clearAllTables
            echo ""
            echo -e "${GREEN}✅ Database clear completed!${NC}"
        else
            echo -e "${YELLOW}❌ Confirmation failed. Operation cancelled.${NC}"
            exit 1
        fi
        ;;
    
    2)
        echo ""
        echo -e "${YELLOW}📋 Available tables:${NC}"
        echo "users, customers, projects, logEntries, logLikes, messages,"
        echo "messageReactions, messageReadStatus, userProjectNotes,"
        echo "subcontractorAssignments, teamInvitations, customerSessions,"
        echo "subscriptions, subscriptionEvents, subscriptionUsage,"
        echo "seatUsageHistory, billingEvents, dunningCampaigns,"
        echo "dunningConfigurations, paymentFailures, subscriptionAnalytics,"
        echo "planChangeRequests, subscriptionCancellations,"
        echo "billingPortalConfigurations, billingPortalSessions,"
        echo "checkoutSessions, webhookEvents, webhookDeliveries,"
        echo "auditLogs, systemNotifications, userNotifications,"
        echo "notificationPreferences, emailTemplates, emailDeliveries,"
        echo "systemSettings, featureFlags, maintenanceWindows,"
        echo "apiKeys, rateLimits, errorLogs, performanceMetrics,"
        echo "backupMetadata"
        echo ""
        read -p "Enter table names (comma-separated): " tables
        
        if [ -z "$tables" ]; then
            echo -e "${YELLOW}❌ No tables specified. Operation cancelled.${NC}"
            exit 1
        fi
        
        echo ""
        echo -e "${YELLOW}⚠️  You are about to delete data from: ${tables}${NC}"
        read -p "Type 'CONFIRM DELETE' to proceed: " confirmation
        
        if [ "$confirmation" = "CONFIRM DELETE" ]; then
            echo ""
            echo -e "${BLUE}🗑️  Clearing specified tables...${NC}"
            
            # Convert comma-separated string to JSON array format for Convex
            IFS=',' read -ra TABLE_ARRAY <<< "$tables"
            table_json="["
            for i in "${!TABLE_ARRAY[@]}"; do
                table_name=$(echo "${TABLE_ARRAY[$i]}" | xargs) # trim whitespace
                if [ $i -gt 0 ]; then
                    table_json+=","
                fi
                table_json+="\"$table_name\""
            done
            table_json+="]"
            
            npx convex run scripts/clearDatabase:clearSpecificTables "{\"tableNames\": $table_json}"
            echo ""
            echo -e "${GREEN}✅ Table clearing completed!${NC}"
        else
            echo -e "${YELLOW}❌ Confirmation failed. Operation cancelled.${NC}"
            exit 1
        fi
        ;;
    
    3)
        echo -e "${GREEN}✅ Operation cancelled safely.${NC}"
        exit 0
        ;;
    
    *)
        echo -e "${RED}❌ Invalid option. Please choose 1, 2, or 3.${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}💡 Tip: You can also run the functions directly:${NC}"
echo "  npx convex run scripts/clearDatabase:clearAllTables"
echo "  npx convex run scripts/clearDatabase:clearSpecificTables '{\"tableNames\": [\"users\", \"projects\"]}'"
