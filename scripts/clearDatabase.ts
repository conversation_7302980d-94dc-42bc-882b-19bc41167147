/**
 * <PERSON><PERSON><PERSON> for clearing all data from Convex database tables
 * 
 * WARNING: This will permanently delete ALL data in your database!
 * Use with extreme caution and only in development environments.
 * 
 * Usage:
 * npx convex run scripts/clearDatabase:clearAllTables
 */

import { internalMutation } from "../convex/_generated/server";
import { internal } from "../convex/_generated/api";

// List of all tables in the database (from schema.ts)
const ALL_TABLES = [
  "users",
  "customers", 
  "projects",
  "logEntries",
  "logLikes",
  "messages",
  "messageReactions",
  "messageReadStatus",
  "userProjectNotes",
  "subcontractorAssignments",
  "teamInvitations",
  "customerSessions",
  "subscriptions",
  "subscriptionEvents",
  "subscriptionUsage",
  "seatUsageHistory",
  "billingEvents",
  "dunningCampaigns",
  "dunningConfigurations",
  "paymentFailures",
  "subscriptionAnalytics",
  "planChangeRequests",
  "subscriptionCancellations",
  "billingPortalConfigurations",
  "billingPortalSessions",
  "checkoutSessions",
  "webhookEvents",
  "webhookDeliveries",
  "auditLogs",
  "systemNotifications",
  "userNotifications",
  "notificationPreferences",
  "emailTemplates",
  "emailDeliveries",
  "systemSettings",
  "featureFlags",
  "maintenanceWindows",
  "apiKeys",
  "rateLimits",
  "errorLogs",
  "performanceMetrics",
  "backupMetadata"
] as const;

/**
 * Clear all documents from a specific table
 */
export const clearTable = internalMutation({
  args: {},
  handler: async (ctx, args) => {
    const tableName = args.tableName as keyof typeof ctx.db;
    
    console.log(`🗑️  Clearing table: ${tableName}`);
    
    try {
      // Get all documents from the table
      const documents = await ctx.db.query(tableName).collect();
      
      console.log(`📊 Found ${documents.length} documents in ${tableName}`);
      
      // Delete each document
      let deletedCount = 0;
      for (const doc of documents) {
        await ctx.db.delete(doc._id);
        deletedCount++;
        
        // Log progress for large tables
        if (deletedCount % 100 === 0) {
          console.log(`   Deleted ${deletedCount}/${documents.length} documents from ${tableName}`);
        }
      }
      
      console.log(`✅ Successfully cleared ${deletedCount} documents from ${tableName}`);
      return { tableName, deletedCount };
      
    } catch (error) {
      console.error(`❌ Error clearing table ${tableName}:`, error);
      throw error;
    }
  },
});

/**
 * Clear all tables in the database
 * WARNING: This will delete ALL data!
 */
export const clearAllTables = internalMutation({
  args: {},
  handler: async (ctx) => {
    console.log("🚨 WARNING: Starting database clear operation!");
    console.log("This will permanently delete ALL data from ALL tables.");
    
    const results = [];
    let totalDeleted = 0;
    
    for (const tableName of ALL_TABLES) {
      try {
        console.log(`\n🗑️  Processing table: ${tableName}`);
        
        // Get all documents from the table
        const documents = await ctx.db.query(tableName as any).collect();
        
        if (documents.length === 0) {
          console.log(`📭 Table ${tableName} is already empty`);
          results.push({ tableName, deletedCount: 0, status: 'empty' });
          continue;
        }
        
        console.log(`📊 Found ${documents.length} documents in ${tableName}`);
        
        // Delete each document
        let deletedCount = 0;
        for (const doc of documents) {
          await ctx.db.delete(doc._id);
          deletedCount++;
          
          // Log progress for large tables
          if (deletedCount % 50 === 0) {
            console.log(`   Progress: ${deletedCount}/${documents.length} documents deleted from ${tableName}`);
          }
        }
        
        console.log(`✅ Successfully cleared ${deletedCount} documents from ${tableName}`);
        results.push({ tableName, deletedCount, status: 'cleared' });
        totalDeleted += deletedCount;
        
      } catch (error) {
        console.error(`❌ Error processing table ${tableName}:`, error);
        results.push({ tableName, deletedCount: 0, status: 'error', error: error.message });
      }
    }
    
    console.log("\n🏁 Database clear operation completed!");
    console.log(`📊 Total documents deleted: ${totalDeleted}`);
    console.log("\n📋 Summary:");
    
    results.forEach(result => {
      const status = result.status === 'cleared' ? '✅' : 
                    result.status === 'empty' ? '📭' : '❌';
      console.log(`${status} ${result.tableName}: ${result.deletedCount} documents`);
    });
    
    return {
      totalDeleted,
      results,
      timestamp: Date.now()
    };
  },
});

/**
 * Clear specific tables only
 */
export const clearSpecificTables = internalMutation({
  args: {},
  handler: async (ctx, args) => {
    const tableNames = args.tableNames as string[];
    
    console.log(`🗑️  Clearing specific tables: ${tableNames.join(', ')}`);
    
    const results = [];
    let totalDeleted = 0;
    
    for (const tableName of tableNames) {
      if (!ALL_TABLES.includes(tableName as any)) {
        console.warn(`⚠️  Unknown table: ${tableName} - skipping`);
        results.push({ tableName, deletedCount: 0, status: 'unknown' });
        continue;
      }
      
      try {
        const documents = await ctx.db.query(tableName as any).collect();
        
        if (documents.length === 0) {
          console.log(`📭 Table ${tableName} is already empty`);
          results.push({ tableName, deletedCount: 0, status: 'empty' });
          continue;
        }
        
        console.log(`📊 Clearing ${documents.length} documents from ${tableName}`);
        
        let deletedCount = 0;
        for (const doc of documents) {
          await ctx.db.delete(doc._id);
          deletedCount++;
        }
        
        console.log(`✅ Successfully cleared ${deletedCount} documents from ${tableName}`);
        results.push({ tableName, deletedCount, status: 'cleared' });
        totalDeleted += deletedCount;
        
      } catch (error) {
        console.error(`❌ Error clearing table ${tableName}:`, error);
        results.push({ tableName, deletedCount: 0, status: 'error', error: error.message });
      }
    }
    
    console.log(`\n🏁 Cleared ${totalDeleted} total documents from ${tableNames.length} tables`);
    
    return {
      totalDeleted,
      results,
      timestamp: Date.now()
    };
  },
});
