# Database Clear Scripts

Dette er verktøy for å slette data fra Convex-databasen din. **BRUK MED FORSIKTIGHET!**

## ⚠️ ADVARSEL

Disse scriptene vil **permanent slette** data fra databasen din. Dette kan ikke angres! Bruk kun i utviklingsmiljø eller når du er helt sikker på at du vil slette dataene.

## 📁 Filer

- `clearDatabase.ts` - Convex-funksjoner for å slette data
- `clear-database.sh` - Interaktivt bash-script for enkel bruk
- `README.md` - Denne filen

## 🚀 Bruk

### Metode 1: Interaktivt script (anbefalt)

```bash
./scripts/clear-database.sh
```

Dette scriptet gir deg følgende alternativer:
1. **Slett ALLE tabeller** - Komplett database-wipe
2. **Slett spesifikke tabeller** - Velg hvilke tabeller du vil slette
3. **Av<PERSON>ryt** - Avslutt trygt uten å gjøre endringer

### Metode 2: Direkte Convex-kommandoer

#### Slett alle tabeller:
```bash
npx convex run scripts/clearDatabase:clearAllTables
```

#### Slett spesifikke tabeller:
```bash
npx convex run scripts/clearDatabase:clearSpecificTables '{"tableNames": ["users", "projects", "logEntries"]}'
```

## 📊 Tabeller i databasen

Databasen inneholder følgende tabeller:

### Kjernetabeller
- `users` - Brukerkontoer og team-administrasjon
- `customers` - Kunde-/bedriftsdata
- `projects` - Prosjektadministrasjon
- `logEntries` - Prosjektaktivitetslogger
- `messages` - Chat-system

### Abonnement og fakturering
- `subscriptions` - Stripe-abonnementer
- `subscriptionEvents` - Abonnementshendelser
- `billingEvents` - Faktureringshendelser
- `checkoutSessions` - Checkout-økter

### Team og tilganger
- `teamInvitations` - Team-invitasjoner
- `subcontractorAssignments` - Underentreprenør-oppdrag
- `customerSessions` - Kunde-økter

### System og logging
- `auditLogs` - Revisjonslogger
- `systemNotifications` - Systemvarsler
- `errorLogs` - Feillogger
- `webhookEvents` - Webhook-hendelser

### Og mange flere...

Se `clearDatabase.ts` for komplett liste.

## 🔒 Sikkerhet

- Scriptene krever eksplisitt bekreftelse før sletting
- Du må skrive nøyaktig bekreftelsestekst for å fortsette
- Alle operasjoner logges med detaljert informasjon
- Feil håndteres gracefully uten å stoppe hele operasjonen

## 📝 Eksempler

### Slett kun testdata:
```bash
npx convex run scripts/clearDatabase:clearSpecificTables '{"tableNames": ["users", "projects", "logEntries"]}'
```

### Slett kun abonnementsdata:
```bash
npx convex run scripts/clearDatabase:clearSpecificTables '{"tableNames": ["subscriptions", "subscriptionEvents", "billingEvents"]}'
```

### Komplett reset av databasen:
```bash
npx convex run scripts/clearDatabase:clearAllTables
```

## 🐛 Feilsøking

Hvis du får feil:

1. **Convex ikke tilkoblet**: Kjør `npx convex dev` først
2. **Ukjent tabell**: Sjekk at tabellnavnet er riktig stavet
3. **Tilgangsfeil**: Sørg for at du er logget inn i riktig Convex-prosjekt

## 💡 Tips

- Bruk det interaktive scriptet (`./scripts/clear-database.sh`) for enklest bruk
- Test først med spesifikke tabeller før du sletter alt
- Ta backup hvis du har viktige data (bruk Convex dashboard)
- Scriptene viser progress for store tabeller

## 🔄 Gjenoppretting

Hvis du sletter data ved en feil:
1. Sjekk om du har backup i Convex dashboard
2. Gjenopprett fra backup hvis tilgjengelig
3. Eller kjør onboarding-prosessen på nytt for å lage nye testdata
