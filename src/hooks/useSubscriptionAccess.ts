import { useUser } from '@clerk/clerk-react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { useSubscriptionRefresh } from '../contexts/SubscriptionRefreshContext';

/**
 * Hook to manage subscription access control
 * Returns subscription status and access permissions
 */
export const useSubscriptionAccess = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Make refresh context optional to avoid breaking existing functionality
  let refreshKey = 0;
  try {
    const refreshContext = useSubscriptionRefresh();
    refreshKey = refreshContext.refreshKey;
  } catch (error) {
    // SubscriptionRefreshProvider not available, continue without it
    console.log('SubscriptionRefreshProvider not available, continuing without refresh functionality');
  }

  // Only query when user is loaded and authenticated
  const shouldQuery = isClerkLoaded && user?.id;

  const subscriptionStatus = useQuery(
    api.subscriptions.getSubscriptionStatus,
    shouldQuery ? { userId: user.id } : "skip"
  );

  const subscription = useQuery(
    api.subscriptions.getUserSubscription,
    shouldQuery ? { userId: user.id } : "skip"
  );

  // Loading state
  const isLoading = !isClerkLoaded || subscriptionStatus === undefined;

  // Add debugging
  console.log('🔍 useSubscriptionAccess Debug:', {
    isClerkLoaded,
    userId: user?.id,
    subscriptionStatus: subscriptionStatus ? 'exists' : 'null',
    subscriptionStatusDetail: subscriptionStatus,
    subscription: subscription ? 'exists' : 'null',
    subscriptionDetail: subscription,
    isLoading,
    hasSubscription: (subscriptionStatus as any)?.hasSubscription
  });

  // If no subscription data, return default values
  if (!subscriptionStatus || !(subscriptionStatus as any)?.hasSubscription) {
    console.log('❌ No subscription found, showing trial setup');
    return {
      isLoading,
      subscription: null,
      hasActiveSubscription: false,
      isInTrial: false,
      isTrialExpired: false,
      isInGracePeriod: false,
      canCreateProjects: false,
      canAccessProjects: false,
      hasFullAccess: false,
      isReadOnly: false,
      needsUpgrade: true,
      needsTrialSetup: true,
    };
  }

  // Derive access permissions from subscription status
  const {
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    isCanceled,
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isReadOnly,
    needsUpgrade,
  } = subscriptionStatus;

  console.log('✅ Active subscription found:', {
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    needsUpgrade,
    subscription: subscription ? 'exists' : 'null'
  });

  return {
    isLoading,
    subscription,
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    isCanceled,
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isReadOnly,
    needsUpgrade,
    needsTrialSetup: false,
  };
};

/**
 * Hook to get seat management information
 */
export const useSeatManagement = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Only query when user is loaded and authenticated
  const shouldQuery = isClerkLoaded && user?.id;

  const seatCheck = useQuery(
    api.seatManagement.canInviteTeamMember,
    shouldQuery ? { userId: user.id } : "skip"
  );

  const subscription2 = useQuery(
    api.subscriptions.getUserSubscription,
    shouldQuery ? { userId: user.id } : "skip"
  );

  const isLoading = !isClerkLoaded || seatCheck === undefined || subscription2 === undefined;

  if (!seatCheck || !subscription2) {
    return {
      isLoading,
      canInvite: false,
      currentSeats: 0,
      maxSeats: 0,
      remainingSeats: 0,
      warning: null,
      warningMessage: null,
      isNearLimit: false,
      isCritical: false,
      isAtLimit: false,
    };
  }

  const currentSeats = (subscription2 as any)?.seats || 0;
  const maxSeats = (seatCheck as any)?.maxSeats || 0;
  const remainingSeats = maxSeats - currentSeats;
  const isAtLimit = currentSeats >= maxSeats;
  const isCritical = (seatCheck as any)?.warning === "critical";
  const isNearLimit = (seatCheck as any)?.warning === "approaching" || isCritical;

  return {
    isLoading,
    canInvite: (seatCheck as any)?.canInvite,
    currentSeats,
    maxSeats,
    remainingSeats,
    warning: (seatCheck as any)?.warning,
    warningMessage: (seatCheck as any)?.warningMessage,
    isNearLimit,
    isCritical,
    isAtLimit,
    suggestedPlan: (seatCheck as any)?.suggestedPlan,
    blockedReason: (seatCheck as any)?.reason,
    blockedMessage: (seatCheck as any)?.message,
  };
};
