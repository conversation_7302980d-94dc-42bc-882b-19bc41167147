import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRealTimeSubscriptionAccess } from '../../hooks/useRealTimeSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import './ModernTrialBanner.css';

interface CanceledSubscriptionBannerProps {
  className?: string;
}

/**
 * Canceled subscription banner component - matches ModernTrialBanner design
 * Sticky positioning, mobile-first responsive design, shows when subscription is canceled
 */
export const CanceledSubscriptionBanner: React.FC<CanceledSubscriptionBannerProps> = ({
  className = ''
}) => {
  const navigate = useNavigate();
  const {
    subscription,
    isCanceled,
    hasActiveSubscription
  } = useRealTimeSubscriptionAccess();
  const { isAdministrator } = useUserRole();
  const [isDismissed, setIsDismissed] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Check if banner should be shown - only for canceled subscriptions
  const shouldShow = isCanceled && !isDismissed && subscription && !hasActiveSubscription;

  // Animation effect
  useEffect(() => {
    if (shouldShow) {
      const timer = setTimeout(() => setIsVisible(true), 100);
      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [shouldShow]);

  // Handle dismiss (temporary - comes back next session)
  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => setIsDismissed(true), 300); // Wait for animation
  };

  // Handle CTA click
  const handleReactivate = () => {
    if (isAdministrator) {
      navigate('/subscription');
    } else {
      // For non-administrators, show message or redirect
      navigate('/subscription');
    }
  };

  // Don't render if shouldn't show
  if (!shouldShow) return null;

  // Get cancellation date for display
  const getCancellationDate = () => {
    if (subscription?.canceledAt) {
      return new Date(subscription.canceledAt).toLocaleDateString('nb-NO');
    }
    return 'ukjent dato';
  };

  return (
    <div
      className={`fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ease-out ${
        isVisible ? 'translate-y-0' : '-translate-y-full'
      } ${className}`}
      data-testid="canceled-subscription-banner"
      data-analytics="canceled-subscription-banner"
    >
      <div
        className="min-h-[48px] md:min-h-[56px] flex items-center shadow-medium bg-jobblogg-error text-white"
        role="banner"
        aria-live="polite"
        aria-label="Kansellert abonnement varsel"
      >
        <div className="container-wide px-4 sm:px-6">
          <div className="flex items-center justify-between gap-4 py-2.5 md:py-3">
            {/* Icon + Text Section */}
            <div className="flex items-center gap-4 flex-1 min-w-0">
              {/* Warning Icon */}
              <div
                className="flex-shrink-0 w-7 h-7 flex items-center justify-center"
                aria-hidden="true"
              >
                <span className="text-xl leading-none">❌</span>
              </div>
              
              {/* Main Text */}
              <div className="flex-1 min-w-0">
                <p className="text-sm sm:text-base font-semibold leading-tight font-system">
                  Abonnement kansellert {getCancellationDate()}
                  {isAdministrator && (
                    <span className="block sm:inline sm:ml-2 text-xs sm:text-sm font-medium opacity-90 mt-1 sm:mt-0">
                      Du har ikke lenger tilgang til å opprette eller se prosjekter
                    </span>
                  )}
                </p>
              </div>
            </div>

            {/* Actions Section */}
            <div className="flex items-center gap-2 flex-shrink-0">
              {/* Dismiss Button */}
              <button
                onClick={handleDismiss}
                className="p-2 rounded-lg hover:bg-white/10 transition-colors duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center"
                aria-label="Skjul varsel"
                title="Skjul varsel"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              {/* CTA Button */}
              {isAdministrator && (
                <button
                  onClick={handleReactivate}
                  className="
                    inline-flex items-center justify-center gap-2 px-3 py-2 sm:px-4 sm:py-2
                    rounded-xl text-sm font-semibold transition-all duration-200 ease-in-out
                    min-h-[44px] min-w-[44px] shadow-soft hover:shadow-medium
                    focus:outline-none focus:ring-2 focus:ring-offset-2
                    active:scale-[0.98] active:transition-transform active:duration-150
                    transform hover:scale-[1.02] hover:translate-y-[-1px]
                    bg-white text-jobblogg-error hover:bg-gray-50 hover:text-jobblogg-error-dark 
                    focus:ring-white focus:ring-offset-jobblogg-error
                  "
                  aria-label="Reaktiver abonnement"
                >
                  <span className="hidden sm:inline">Reaktiver</span>
                  <span className="sm:hidden">Reaktiver</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
