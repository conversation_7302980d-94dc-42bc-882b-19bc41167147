import { useUser } from '@clerk/clerk-react';
import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface MobileUpgradeBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  feature: 'create_project' | 'team_management' | 'project_sharing' | 'file_upload' | 'full_access';
  reason?: 'trial_expired' | 'grace_period' | 'no_subscription';
}

const FEATURE_INFO = {
  create_project: {
    title: 'Opprett Prosjekter',
    description: 'Opprett nye prosjekter og dokumenter arbeidet ditt',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
    ),
    benefits: [
      'Ubegrenset antall prosjekter',
      'Detaljert prosjektdokumentasjon',
      'Automatisk backup og synkronisering'
    ]
  },
  team_management: {
    title: 'Teamadministrasjon',
    description: 'Administrer teammedlemmer og roller',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    ),
    benefits: [
      'Inviter ubegrenset antall teammedlemmer',
      'Administrer roller og tilganger',
      'Teamsamarbeid på prosjekter'
    ]
  },
  project_sharing: {
    title: 'Prosjektdeling',
    description: 'Del prosjekter med kunder og samarbeidspartnere',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
      </svg>
    ),
    benefits: [
      'Del prosjekter med kunder',
      'Kontrollerte tilganger',
      'Automatiske oppdateringer'
    ]
  },
  file_upload: {
    title: 'Filopplasting',
    description: 'Last opp bilder og dokumenter til prosjektene dine',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
      </svg>
    ),
    benefits: [
      'Ubegrenset filopplasting',
      'Automatisk bildekomprimering',
      'Sikker skylagring'
    ]
  },
  full_access: {
    title: 'Full Tilgang',
    description: 'Få tilgang til alle JobbLogg-funksjoner',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>
    ),
    benefits: [
      'Alle premium-funksjoner',
      'Prioritert kundesupport',
      'Avanserte rapporter'
    ]
  }
};

export const MobileUpgradeBottomSheet: React.FC<MobileUpgradeBottomSheetProps> = ({
  isOpen,
  onClose,
  feature,
  reason = 'trial_expired'
}) => {
  const { user } = useUser();
  const [isAnimating, setIsAnimating] = useState(false);

  const featureInfo = FEATURE_INFO[feature];

  // Handle escape key and prevent body scroll
  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
      document.body.style.overflow = 'hidden';
      
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose();
        }
      };
      
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('keydown', handleEscape);
        document.body.style.overflow = 'unset';
      };
    } else {
      document.body.style.overflow = 'unset';
      setIsAnimating(false);
    }
  }, [isOpen, onClose]);

  const handleUpgrade = () => {
    if (!user) return;
    window.location.href = '/subscription';
  };

  const getReasonText = () => {
    switch (reason) {
      case 'trial_expired':
        return 'Prøveperioden er utløpt';
      case 'grace_period':
        return 'Begrenset tilgang';
      case 'no_subscription':
        return 'Krever abonnement';
      default:
        return 'Ikke tilgjengelig';
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return createPortal(
    <div 
      className="fixed inset-0 z-50 flex items-end justify-center"
      onClick={handleBackdropClick}
    >
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isAnimating ? 'opacity-50' : 'opacity-0'
        }`}
      />
      
      {/* Bottom Sheet */}
      <div 
        className={`relative w-full max-w-lg bg-white rounded-t-2xl shadow-2xl transform transition-transform duration-300 ease-out ${
          isAnimating ? 'translate-y-0' : 'translate-y-full'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Handle bar */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
        </div>

        {/* Content */}
        <div className="px-6 pb-8">
          {/* Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-jobblogg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="text-jobblogg-primary">
                {featureInfo.icon}
              </div>
            </div>
            <h2 className="text-xl font-bold text-jobblogg-text-strong mb-2">
              {featureInfo.title}
            </h2>
            <p className="text-sm text-jobblogg-text-medium mb-1">
              {featureInfo.description}
            </p>
            <p className="text-xs text-jobblogg-warning font-medium">
              {getReasonText()}
            </p>
          </div>

          {/* Benefits */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-jobblogg-text-strong mb-3">
              Med JobbLogg får du:
            </h3>
            <ul className="space-y-2">
              {featureInfo.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start gap-3">
                  <div className="w-5 h-5 bg-jobblogg-success/10 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <svg className="w-3 h-3 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-sm text-jobblogg-text-medium">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Action buttons */}
          <div className="space-y-3">
            {/* Primary upgrade button - 44px minimum touch target */}
            <button
              onClick={handleUpgrade}
              className="w-full h-12 bg-jobblogg-primary text-white font-semibold rounded-xl hover:bg-jobblogg-primary-dark active:bg-jobblogg-primary-dark transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              Oppgrader til Mellomstor bedrift
            </button>
            
            {/* Secondary close button - 44px minimum touch target */}
            <button
              onClick={onClose}
              className="w-full h-12 bg-gray-100 text-jobblogg-text-medium font-medium rounded-xl hover:bg-gray-200 active:bg-gray-200 transition-colors duration-200"
            >
              Lukk
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};
