import React from 'react';
import { Link } from 'react-router-dom';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';

interface PlanInfo {
  name: string;
  badge: string;
  color: string;
}

const getPlanInfo = (planLevel: string): PlanInfo => {
  switch (planLevel) {
    case 'basic':
      return {
        name: 'Liten bedrift',
        badge: 'Liten bedrift',
        color: 'text-jobblogg-text-medium'
      };
    case 'professional':
      return {
        name: '<PERSON><PERSON><PERSON><PERSON> bedrift',
        badge: '<PERSON><PERSON><PERSON><PERSON> bedrift',
        color: 'text-jobblogg-primary'
      };
    case 'enterprise':
      return {
        name: 'Stor bedrift',
        badge: 'Stor bedrift',
        color: 'text-jobblogg-success'
      };
    default:
      return {
        name: 'Ukjent plan',
        badge: 'Ukjent plan',
        color: 'text-jobblogg-text-muted'
      };
  }
};

export const ActivePlanIndicator: React.FC = () => {
  const { subscription, isLoading } = useSubscriptionAccess();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();

  // Debug logging
  console.log('🔍 ActivePlanIndicator Debug:', {
    isLoading,
    roleLoading,
    subscription,
    planLevel: (subscription as any)?.planLevel,
    billingInterval: (subscription as any)?.billingInterval,
    shouldShow: !isLoading && !roleLoading && subscription
  });

  // Don't show if loading or no subscription
  if (isLoading || roleLoading || !subscription) return null;

  const planLevel = (subscription as any)?.planLevel || 'basic';
  const billingInterval = (subscription as any)?.billingInterval || 'month';
  const planInfo = getPlanInfo(planLevel);

  // Non-administrator view - just show plan info
  if (!isAdministrator) {
    return (
      <div className="p-3 rounded-lg border border-jobblogg-border bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium text-jobblogg-text-strong">
                Aktiv plan:
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className={`text-sm font-semibold ${planInfo.color}`}>
                {planInfo.name}
              </span>
              <span className="text-xs text-jobblogg-text-muted">
                ({billingInterval === 'year' ? 'Årlig' : 'Månedlig'})
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Administrator view - clickable to manage subscription
  return (
    <Link
      to="/subscription"
      className="block p-3 rounded-lg border border-jobblogg-border bg-white hover:bg-jobblogg-surface transition-colors duration-200"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm font-medium text-jobblogg-text-strong">
              Aktiv plan:
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className={`text-sm font-semibold ${planInfo.color}`}>
              {planInfo.name}
            </span>
            <span className="text-xs text-jobblogg-text-muted">
              ({billingInterval === 'year' ? 'Årlig' : 'Månedlig'})
            </span>
          </div>
        </div>
        <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </Link>
  );
};
