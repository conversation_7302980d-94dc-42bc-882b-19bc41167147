import React from 'react';
import { useRealTimeSubscriptionAccess } from '../../hooks/useRealTimeSubscriptionAccess';

/**
 * Spacer component to prevent content from being hidden behind the sticky banners
 * Only renders when trial or canceled subscription banners are visible
 */
export const TrialBannerSpacer: React.FC = () => {
  const {
    subscription,
    isInTrial,
    shouldShowExpiredStatus,
    isCanceled,
    hasActiveSubscription
  } = useRealTimeSubscriptionAccess();

  // Show spacer when trial banner would be visible
  const shouldShowTrialSpacer = (isInTrial || shouldShowExpiredStatus) && subscription && !hasActiveSubscription;

  // Show spacer when canceled subscription banner would be visible
  const shouldShowCanceledSpacer = isCanceled && subscription && !hasActiveSubscription;

  // Show spacer if any banner is visible
  const shouldShowSpacer = shouldShowTrialSpacer || shouldShowCanceledSpacer;

  if (!shouldShowSpacer) return null;

  return (
    <div
      className="modern-trial-banner-spacer"
      aria-hidden="true"
      data-testid="trial-banner-spacer"
    />
  );
};
