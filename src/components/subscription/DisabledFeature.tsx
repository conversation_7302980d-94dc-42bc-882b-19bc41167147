import { useUser } from '@clerk/clerk-react';
import React, { useState } from 'react';
import { useDeviceCapabilities, useIsMobile } from '../../hooks/useResponsive';
import { BodyText, Heading2, Modal, PrimaryButton, SecondaryButton } from '../ui';
import { MobileUpgradeBottomSheet } from './MobileUpgradeBottomSheet';

interface DisabledFeatureProps {
  children: React.ReactNode;
  feature: 'create_project' | 'team_management' | 'project_sharing' | 'file_upload' | 'full_access';
  reason?: 'trial_expired' | 'grace_period' | 'no_subscription' | 'subscription_canceled';
  showUpgradeModal?: boolean;
  className?: string;
}

interface FeatureInfo {
  title: string;
  description: string;
  icon: React.ReactNode;
  upgradeMessage: string;
}

const FEATURE_INFO: Record<string, FeatureInfo> = {
  create_project: {
    title: 'Opprett nye prosjekter',
    description: 'Opprett og administrer ubegrenset antall prosjekter',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
      </svg>
    ),
    upgradeMessage: 'For å opprette nye prosjekter trenger du et aktivt abonnement.'
  },
  team_management: {
    title: 'Administrer team',
    description: 'Inviter teammedlemmer og administrer roller',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    ),
    upgradeMessage: 'For å administrere teammedlemmer trenger du et aktivt abonnement.'
  },
  project_sharing: {
    title: 'Del prosjekter',
    description: 'Del prosjekter med kunder og samarbeidspartnere',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
      </svg>
    ),
    upgradeMessage: 'For å dele prosjekter med kunder trenger du et aktivt abonnement.'
  },
  file_upload: {
    title: 'Last opp filer',
    description: 'Last opp bilder og dokumenter til prosjekter',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
      </svg>
    ),
    upgradeMessage: 'For å laste opp filer trenger du et aktivt abonnement.'
  },
  full_access: {
    title: 'Full tilgang',
    description: 'Tilgang til alle JobbLogg-funksjoner',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>
    ),
    upgradeMessage: 'For å få full tilgang til alle funksjoner trenger du et aktivt abonnement.'
  }
};

export const DisabledFeature: React.FC<DisabledFeatureProps> = ({
  children,
  feature,
  reason = 'trial_expired',
  showUpgradeModal = true,
  className = ''
}) => {
  const { user } = useUser();
  const [showModal, setShowModal] = useState(false);
  const [showMobileBottomSheet, setShowMobileBottomSheet] = useState(false);
  const isMobile = useIsMobile();
  const { hasVibration } = useDeviceCapabilities();

  const featureInfo = FEATURE_INFO[feature];
  
  const handleUpgrade = async () => {
    if (!user) return;
    
    // TODO: Implement upgrade flow
    console.log('Upgrade flow triggered for feature:', feature);
    // This would typically redirect to subscription management or Stripe checkout
    window.location.href = '/subscription';
  };

  const getReasonText = () => {
    switch (reason) {
      case 'trial_expired':
        return 'Prøveperioden er utløpt';
      case 'grace_period':
        return 'Begrenset tilgang';
      case 'no_subscription':
        return 'Krever abonnement';
      case 'subscription_canceled':
        return 'Abonnement kansellert';
      default:
        return 'Ikke tilgjengelig';
    }
  };

  return (
    <>
      <div className={`relative ${className}`}>
        {/* Disabled overlay */}
        <div className="relative">
          {/* Content with disabled styling */}
          <div className="opacity-50 pointer-events-none select-none">
            {children}
          </div>
          
          {/* Lock overlay */}
          <div className="absolute inset-0 bg-white/80 backdrop-blur-[1px] rounded-lg flex items-center justify-center">
            <div className="text-center p-4 max-w-xs">
              {/* Lock icon */}
              <div className="w-12 h-12 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              
              {/* Feature info */}
              <h3 className="font-semibold text-jobblogg-text-strong text-sm mb-1">
                {featureInfo.title}
              </h3>
              <p className="text-xs text-jobblogg-text-medium mb-3">
                {getReasonText()}
              </p>
              
              {/* Upgrade button - mobile optimized */}
              {showUpgradeModal && (
                <button
                  onClick={() => {
                    if (isMobile) {
                      setShowMobileBottomSheet(true);
                      // Haptic feedback on mobile
                      if (hasVibration) {
                        navigator.vibrate(50);
                      }
                    } else {
                      setShowModal(true);
                    }
                  }}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-jobblogg-primary text-white text-sm font-medium rounded-lg hover:bg-jobblogg-primary-dark active:bg-jobblogg-primary-dark transition-colors duration-200 min-h-[44px] min-w-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  Oppgrader
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Modal */}
      {showModal && (
        <Modal isOpen={showModal} onClose={() => setShowModal(false)} title="Oppgrader abonnement">
          <div className="space-y-6">
            {/* Feature highlight */}
            <div className="text-center">
              <div className="w-16 h-16 bg-jobblogg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="text-jobblogg-primary">
                  {featureInfo.icon}
                </div>
              </div>
              <Heading2 className="mb-2">{featureInfo.title}</Heading2>
              <BodyText className="text-jobblogg-text-medium">
                {featureInfo.upgradeMessage}
              </BodyText>
            </div>

            {/* Benefits */}
            <div className="bg-jobblogg-surface rounded-lg p-4">
              <h4 className="font-semibold text-jobblogg-text-strong mb-3">Med et aktivt abonnement får du:</h4>
              <ul className="space-y-2 text-sm text-jobblogg-text-medium">
                <li className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-jobblogg-success flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Ubegrenset prosjekter og teammedlemmer
                </li>
                <li className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-jobblogg-success flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Prosjektdeling med kunder
                </li>
                <li className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-jobblogg-success flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Ubegrenset fillagring
                </li>
                <li className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-jobblogg-success flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Prioritert kundesupport
                </li>
              </ul>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <PrimaryButton onClick={handleUpgrade} className="flex-1">
                Se abonnementsplaner
              </PrimaryButton>
              <SecondaryButton onClick={() => setShowModal(false)}>
                Lukk
              </SecondaryButton>
            </div>
          </div>
        </Modal>
      )}

      {/* Mobile bottom sheet */}
      <MobileUpgradeBottomSheet
        isOpen={showMobileBottomSheet}
        onClose={() => setShowMobileBottomSheet(false)}
        feature={feature}
        reason={reason}
      />
    </>
  );
};
