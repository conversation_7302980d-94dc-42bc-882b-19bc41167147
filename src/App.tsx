import { AuthLoading, Authenticated, Unauthenticated } from 'convex/react'
import { useEffect } from 'react'
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom'
import { ContractorOnboardingGuardSimple } from './components/ContractorOnboardingGuardSimple'
import { CookieBanner } from './components/CookieConsent'
import {
    LazyAcceptInvite,
    LazyArchivedProjects,
    LazyBlockedUser,
    LazyCheckoutCancel,
    LazyCheckoutCancelTest,
    LazyCheckoutSuccess,
    LazyCheckoutSuccessTest,
    LazyContractorOnboarding,
    LazyConversations,
    LazyCookiePolicy,
    LazyCreateProject,
    LazyDashboard,
    LazyGoogleMapsTest,
    LazyHelpPage,
    LazyInvitationDetail,
    LazyInvitationsList,
    LazyLandingPage,
    LazyNotifications,
    LazyPageWrapper,
    LazyPricingPage,
    LazyPrivacyPolicy,
    LazyPrivacySettings,
    LazyProjectDetail,
    LazyProjectEdit,
    LazyProjectLog,
    LazyReactivationSuccess,
    LazySharedProject,
    LazySignIn,
    LazySignUp,
    LazyStripeTest,
    LazySubscriptionManagement,
    LazyTeamManagement,
    LazyTeamOnboarding,
    LazyTeamProjects,
    LazyTermsOfService,
    LazyWebhookMonitor,
    preloadCriticalRoutes
} from './components/LazyComponents'
import { OfflineSync, StorageUsage } from './components/OfflineSync'
import { PWAInstallBanner } from './components/PWAInstallBanner'
import { SubscriptionStateDebugger } from './components/debug/SubscriptionStateDebugger'
import { AuthenticatedLayout } from './components/ui/Layout'
import { SubscriptionRefreshProvider } from './contexts/SubscriptionRefreshContext'
import SubscriptionSync from './pages/Debug/SubscriptionSync'
import TestCompanyProfile from './pages/TestCompanyProfile'

function App() {
  // Preload critical routes on app initialization
  useEffect(() => {
    preloadCriticalRoutes();
  }, []);

  return (
    <SubscriptionRefreshProvider>
      <BrowserRouter>
        <div className="min-h-screen bg-white transition-colors duration-300">
        {/* PWA Components */}
        <PWAInstallBanner />
        <OfflineSync />
        <StorageUsage />

        {/* Cookie Consent */}
        <CookieBanner />
        <Routes>
          {/* Public Routes - Accessible to everyone */}
          <Route path="/shared/:sharedId" element={
            <LazyPageWrapper>
              <LazySharedProject />
            </LazyPageWrapper>
          } />
          {/* Privacy and Legal Pages - Publicly accessible */}
          <Route path="/privacy-policy" element={
            <LazyPageWrapper>
              <LazyPrivacyPolicy />
            </LazyPageWrapper>
          } />
          <Route path="/cookie-policy" element={
            <LazyPageWrapper>
              <LazyCookiePolicy />
            </LazyPageWrapper>
          } />
          <Route path="/terms-of-service" element={
            <LazyPageWrapper>
              <LazyTermsOfService />
            </LazyPageWrapper>
          } />
          <Route path="/help" element={
            <LazyPageWrapper>
              <LazyHelpPage />
            </LazyPageWrapper>
          } />
          <Route path="/pricing" element={
            <LazyPageWrapper>
              <LazyPricingPage />
            </LazyPageWrapper>
          } />

          {/* Authentication Routes - Accessible to unauthenticated users */}
          <Route path="/sign-in" element={
            <LazyPageWrapper>
              <LazySignIn />
            </LazyPageWrapper>
          } />
          <Route path="/sign-up" element={
            <LazyPageWrapper>
              <LazySignUp />
            </LazyPageWrapper>
          } />

          {/* Team Invitation - Publicly accessible (handles auth internally) */}
          <Route path="/team-onboarding" element={
            <LazyPageWrapper>
              <LazyTeamOnboarding />
            </LazyPageWrapper>
          } />

          {/* Magic Link Accept Invitation - Publicly accessible */}
          <Route path="/accept-invite/*" element={
            <LazyPageWrapper>
              <LazyAcceptInvite />
            </LazyPageWrapper>
          } />

          {/* Contractor Onboarding - Requires authentication but bypasses onboarding guard */}
          <Route path="/contractor-onboarding/*" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyContractorOnboarding />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Main Route - Landing page for unauthenticated, Dashboard for authenticated */}
          <Route path="/" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyDashboard />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <LazyPageWrapper>
                  <LazyLandingPage />
                </LazyPageWrapper>
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/archived-projects" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyArchivedProjects />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/conversations" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyConversations />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/team" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyTeamManagement />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/team/projects" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyTeamProjects />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Privacy Settings - Protected route */}
          <Route path="/privacy-settings" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyPrivacySettings />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Blocked User Page - Only accessible to authenticated blocked users */}
          <Route path="/blocked" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyBlockedUser />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Invitation Management Routes */}
          <Route path="/invitations" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyInvitationsList />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/invitations/:invitationId" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyInvitationDetail />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/notifications" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyNotifications />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/subscription" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazySubscriptionManagement />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Checkout Routes - Require authentication */}
          <Route path="/checkout/success" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyCheckoutSuccess />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <div className="min-h-screen flex items-center justify-center bg-jobblogg-surface">
                  <div className="text-center">
                    <h1 className="text-2xl font-bold text-jobblogg-text-primary mb-4">Vennligst logg inn</h1>
                    <p className="text-jobblogg-text-medium">Du må være logget inn for å se betalingsbekreftelsen.</p>
                  </div>
                </div>
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center bg-jobblogg-surface">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/checkout/cancel" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyCheckoutCancel />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <div className="min-h-screen flex items-center justify-center bg-jobblogg-surface">
                  <div className="text-center">
                    <h1 className="text-2xl font-bold text-jobblogg-text-primary mb-4">Vennligst logg inn</h1>
                    <p className="text-jobblogg-text-medium">Du må være logget inn for å se denne siden.</p>
                  </div>
                </div>
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center bg-jobblogg-surface">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Reactivation Success Route - Require authentication */}
          <Route path="/reactivation/success" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyReactivationSuccess />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <div className="min-h-screen flex items-center justify-center bg-jobblogg-surface">
                  <div className="text-center">
                    <h1 className="text-2xl font-bold text-jobblogg-text-primary mb-4">Vennligst logg inn</h1>
                    <p className="text-jobblogg-text-medium">Du må være logget inn for å se reaktiveringsresultatet.</p>
                  </div>
                </div>
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center bg-jobblogg-surface">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/create" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyCreateProject />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/create-wizard" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyCreateProject />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/test-google-maps" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyGoogleMapsTest />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/project/:projectId/details" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyProjectDetail />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/project/:projectId/edit" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyProjectEdit />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/project/:projectId" element={
            <>
              <Authenticated>
                <AuthenticatedLayout>
                  <ContractorOnboardingGuardSimple>
                    <LazyPageWrapper>
                      <LazyProjectLog />
                    </LazyPageWrapper>
                  </ContractorOnboardingGuardSimple>
                </AuthenticatedLayout>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          {/* Remove chat route - now using embedded chat */}

          {/* Test Routes - Development only */}
          <Route path="/test-company-profile" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <TestCompanyProfile />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/test-stripe" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyStripeTest />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/test-checkout-success" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyCheckoutSuccessTest />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/test-checkout-cancel" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyCheckoutCancelTest />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/webhook-monitor" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyWebhookMonitor />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/debug-subscription" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <div className="container mx-auto px-4 py-8">
                      <SubscriptionStateDebugger />
                    </div>
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/debug-sync" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <SubscriptionSync />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
        </Routes>
        </div>
      </BrowserRouter>
    </SubscriptionRefreshProvider>
  )
}

export default App
// CI/CD Pipeline Test - Mon Aug 25 09:04:03 CEST 2025
