import { useUser } from '@clerk/clerk-react';
import { useAction } from 'convex/react';
import React, { useState } from 'react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, PrimaryButton, SecondaryButton } from '../../components/ui';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';

const SubscriptionSync: React.FC = () => {
  const { user } = useUser();
  const { subscription } = useSubscriptionAccess();
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const syncSubscription = useAction(api.debug.syncSubscription.syncSubscriptionFromStripe);

  const handleSync = async () => {
    if (!user) return;

    setIsLoading(true);
    setResult(null);

    try {
      const syncResult = await syncSubscription({ userId: user.id });
      setResult(syncResult);
      
      if (syncResult.success) {
        // Force page reload to see updated data
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Sync error:', error);
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageLayout title="Debug: Subscription Sync" showBackButton backUrl="/subscription">
      <div className="max-w-4xl mx-auto space-y-6">
        
        {/* Current Subscription Status */}
        <div className="bg-white rounded-xl border border-jobblogg-border p-6">
          <h2 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
            Current Subscription Status
          </h2>
          
          {subscription ? (
            <div className="space-y-2 text-sm">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="font-medium">Status:</span> {subscription.status}
                </div>
                <div>
                  <span className="font-medium">Cancel at Period End:</span>{' '}
                  <span className={subscription.cancelAtPeriodEnd ? 'text-red-600' : 'text-green-600'}>
                    {subscription.cancelAtPeriodEnd ? 'Yes' : 'No'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Plan:</span> {subscription.planLevel}
                </div>
                <div>
                  <span className="font-medium">Billing:</span> {subscription.billingInterval}
                </div>
                <div>
                  <span className="font-medium">Stripe ID:</span> {subscription.stripeSubscriptionId}
                </div>
                <div>
                  <span className="font-medium">Last Updated:</span>{' '}
                  {subscription.updatedAt ? new Date(subscription.updatedAt).toLocaleString() : 'N/A'}
                </div>
              </div>
            </div>
          ) : (
            <p className="text-jobblogg-text-muted">No subscription found</p>
          )}
        </div>

        {/* Sync Action */}
        <div className="bg-white rounded-xl border border-jobblogg-border p-6">
          <h2 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
            Manual Sync
          </h2>
          
          <p className="text-jobblogg-text-medium mb-4">
            This will fetch the latest subscription data from Stripe and update the local database.
            Use this if you made changes in the Stripe Customer Portal that aren't reflected here.
          </p>

          <div className="flex gap-3">
            <PrimaryButton
              onClick={handleSync}
              disabled={isLoading || !user}
              className="min-w-[120px]"
            >
              {isLoading ? 'Syncing...' : 'Sync Now'}
            </PrimaryButton>
            
            <SecondaryButton
              onClick={() => window.location.reload()}
              disabled={isLoading}
            >
              Refresh Page
            </SecondaryButton>
          </div>
        </div>

        {/* Sync Result */}
        {result && (
          <div className={`rounded-xl border p-6 ${
            result.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <h2 className="text-lg font-semibold mb-4">
              {result.success ? '✅ Sync Result' : '❌ Sync Error'}
            </h2>
            
            {result.success ? (
              <div className="space-y-3">
                <p className="text-green-800">{result.message}</p>
                
                {result.changes && (
                  <div className="space-y-2">
                    <h3 className="font-medium text-green-800">Changes Made:</h3>
                    <div className="space-y-1 text-sm">
                      {result.changes.cancelAtPeriodEnd && (
                        <div className="bg-green-100 p-2 rounded">
                          <strong>Cancel at Period End:</strong>{' '}
                          {String(result.changes.cancelAtPeriodEnd.before)} → {String(result.changes.cancelAtPeriodEnd.after)}
                        </div>
                      )}
                      {result.changes.status && (
                        <div className="bg-green-100 p-2 rounded">
                          <strong>Status:</strong>{' '}
                          {result.changes.status.before} → {result.changes.status.after}
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                {result.success && result.changes && (
                  <p className="text-sm text-green-700 mt-3">
                    Page will refresh automatically in 2 seconds to show updated data...
                  </p>
                )}
              </div>
            ) : (
              <div className="text-red-800">
                <p><strong>Error:</strong> {result.error}</p>
              </div>
            )}
          </div>
        )}

        {/* Debug Info */}
        <div className="bg-gray-50 rounded-xl border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">
            Debug Information
          </h2>
          
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>User ID:</strong> {user?.id || 'Not logged in'}</p>
            <p><strong>Subscription ID:</strong> {subscription?._id || 'N/A'}</p>
            <p><strong>Current Time:</strong> {new Date().toLocaleString()}</p>
          </div>
        </div>

      </div>
    </PageLayout>
  );
};

export default SubscriptionSync;
